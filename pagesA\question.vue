<template>
	<view class="contentBox">
		<view v-for="(questionData,key) in question.data" >
			<view class="title">问卷{{key+1}}. {{questionData.content}}</view>
			<view>
				<view class='line'></view>
				<view class="profile">发布人： {{questionData.publisher}}</view>
				<view class="profile">发布单位： {{questionData.create_corp}}</view>
				<view class="profile">说明： {{questionData.profile}}</view>
				<view class='dashed-line'></view>
				<view class="questionBox" v-for="(item,index) in questionData.properties" :key="index">
					<view v-if="item.type=='select'" class='qutitle'>{{index+1}}. {{item.title}}（单选）</view>
					<view v-else-if="item.type=='multiselect'" class='qutitle'>{{index+1}}. {{item.title}}（可多选）</view>
					<view v-else class='qutitle'>{{index+1}}. {{item.title}}</view>
					<view v-if="item.type=='select'">
						<radio-group class="radioBox"  @change="radioChange($event,item)"  >
							<view v-for="status in item.content" >
								<radio    :key="status.key"  :value="status.key" :checked="index === current"  color="red" >
									{{status.value}}
								</radio>
							</view>
						</radio-group>
					</view>
					<view v-else-if="item.type=='multiselect'">
						<checkbox-group  class="radioBox" @change="checkboxChange($event,item)">
							<checkbox v-for="info in item.content" :key="info.key"   :value="info.key" color="red">
								{{info.value}}
							</checkbox>
						</checkbox-group>
					</view>
					<view v-else-if="item.type=='time'">
						<view style="margin-top:10rpx;">
							<uni-datetime-picker  :border="false" v-model="item.qd_value"  />
						</view>
					</view>
					<view v-else-if="item.type=='text'" style="margin: 20rpx 10rpx;">
						<uni-easyinput type="textarea" autoHeight v-model="item.qd_value" placeholder="请输入内容" ></uni-easyinput>
					</view>
				</view>
			</view>
		</view>
		<view class="btn" @click="submitButton">提交</view>
	</view>
	
</template>
 
<script>
	export default {
 
		data() {
			return {
				question:[],
 
			};
		},
 
		methods: {
			radioChange: function(e, item) {
				this.$set(item, "qd_value", e.detail.value)
			},
			checkboxChange: function (e, item) {
				this.$set(item, "qd_value", e.detail.value.join(','))
			},
			/**
			 * 跳转
			 */
			submitButton: function() {
				console.log(JSON.stringify(this.question))
				let data = JSON.stringify(this.question)
				let finish=true;
				for(let i=0;i<this.question.data.length;i++){
					for(let j=0;j<this.question.data[i].properties.length;j++){
						if (this.question.data[i].properties[j].qd_value==''){
							finish=false;
							let content='问卷'+(i+1)+'第'+(j+1)+'题未完成';
							uni.showModal({
								title: '提示',
								content: content
							});
							break;
						}
					}
					if(!finish){
						break;
					}
				}
				
				if (finish){
					let url = 'wxQuestionnaireData.php?question=' +data
					this.xAjax({
						url: url,
					}, function(res) {
						uni.reLaunch({
							url: '/pages/main',
						})
					})
				}
				
			}
		},
		onLoad(e){
			//console.log(e.data)
			this.question = JSON.parse(e.data)
			uni.showModal({
				title: '提示',
				content: '请先完成以下问卷调查',
				showCancel:false
			});
		}
	};
</script>
 
<style lang="scss">
	.contentBox {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}
	.line{
		width:100%;
	    height:1px;
	    background-color:#cacaca;
		margin: 20rpx 0rpx 20rpx 0rpx;
	  }
	  
	.dashed-line {
	    height:1px;
	    background-color:#cacaca;
	  	margin: 20rpx 20rpx 20rpx 20rpx;
	}
	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #457cde;
		margin: 150rpx 20rpx 20rpx 20rpx;
		text-align: center;
	}
	.profile {
		font-size: 28rpx;
		margin: 20rpx 20rpx 20rpx 20rpx;
	}
 
	.questionBox {
		padding: 0px 20rpx 20rpx 20rpx;
	}
 
	.qutitle {
		display: flex;
		font-size: 28rpx;
		background-color: #f2f2f2;
		padding: 10rpx 20rpx;
	}
 
	.comment {
		background-color: white;
		margin-top: -50rpx;
	}
 
	.radioBox {
		margin: 10rpx 20rpx;
		display: flex;
		flex-direction: column;
	}
 
	radio {
		font-size: 28rpx;
		margin: 10rpx 0px !important;
	}
 
	checkbox {
		font-size: 28rpx;
		margin: 10rpx 0px !important;
	}
 
	.topic {
		font-size: 32rpx;
		font-weight: bold;
		color: #457cde;
		padding: 10rpx 0rpx;
	}
 
	.para-text {
		font-size: 28rpx;
		font-weight: bold;
		color: #444444;
		padding: 10rpx 0rpx;
	}
 
	.btn {
		width: 50%;
		padding: 20rpx 30rpx;
		margin: 40rpx 0rpx;
		font-size: 28rpx;
		color: white;
		background-color: #3677d3;
		border-radius: 10rpx;
		letter-spacing: 4rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>