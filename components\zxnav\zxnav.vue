<template>
	<view class="content">
		<!--      <view :style="[{ height: customBar + 'px'},{ 'line-height' : customBar+20 + 'px'}]"></view> -->
		<!-- <view class="search-customBar" :style="[{ height: customBar + 'px'},{ 'line-height' : customBar+20 + 'px'}]"> -->
		<view class="main-top" :style="[{ paddingTop: statusHeight + 'px'}]">
			<view class="search-box" :style="[{ width:searchWidth+'px'}]">
				<view style="margin:0 10rpx 0 30rpx;font-size: 30rpx;">{{topTitle}}</view>
				<view class="new-search-box" :style="[{height:searchHeight+'px'}]">
					<uni-icons color="#c0c4cc" size="25" type="search" style="margin-left:10rpx"/>
					<input type="text" placeholder="搜索应用" v-model="keyword" @confirm="search" @input="search" class="search" />
					<view class="scan-box">
						<uni-icons v-if="showClear" @click="clear" color="#c0c4cc" size="25" type="clear" />
						<uni-icons color="#000" size="25" type="scan" @click="scanCode" />
					</view>
				</view>
			</view>
		</view>
		<!-- </view> -->
	</view>
</template>

<script>
	export default {
		props: {
			topTitle: {
				type: String,
				default: '',
			},
		},
		data() {
			return {
				title:'在线',
				//customBar: '',
				keyword: '',
				showClear: false,
				statusHeight: '',
				searchHeight: '',
				searchWidth: ''
			}
		},
		created() {
			//this.customBar = getApp().globalData.customBar;
			//this.showfocus = true;
			// this.statusHeight = uni.getSystemInfoSync().statusBarHeight;
			// #ifdef MP-WEIXIN
			this.statusHeight = uni.getMenuButtonBoundingClientRect().top
			this.searchHeight = uni.getMenuButtonBoundingClientRect().height;
			this.searchWidth = uni.getMenuButtonBoundingClientRect().left
			// #endif
			// #ifdef APP-PLUS||H5
			this.statusHeight = 40
			this.searchHeight = 32
			this.searchWidth = 390
			// #endif
			// #ifdef H5
			this.statusHeight = 0
			// #endif
		},
		methods: {
			scanCode(){//二维码扫描
				let that=this
				// #ifdef MP-WEIXIN||APP-PLUS
				uni.scanCode({
					scanType: ['barCode','qrCode','datamatrix','pdf417'],
					success: function (res) {
						if(res.result.indexOf("{")>-1){//表明是json
							that.scanParse(res.result)
							return
						}
						uni.setClipboardData({
							data: res.result,
							success:function(){
								uni.showModal({
									title:"非本系统格式，已复制到粘贴板:"+res.result
								})
							}
						})
					}
				})
				// #endif
				// #ifdef H5
				uni.navigateTo({
					url: '/pagesA/scanCodeH5'
				})
				// #endif
			},
			search() {
				let that=this
				if(this.keyword){
					this.showClear=true
				}else{
					this.showClear=false
				}
				uni.$emit("searchMenu",{"keyword":that.keyword})
			},
			clear() {
				this.keyword=''
				this.showClear=false
				uni.$emit("searchMenu",{"keyword":''})
			}
		}

	}
</script>

<style scoped lang="scss">
	.content {
		/* position: relative; */
	}

	.main-top {
		height:100rpx;
		padding-bottom: 45px;
		position: fixed;
		left: 0;
		top: 0;
		z-index: 99;
		width: 100%;
		background-color: #fafbfc;
		.search-box {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			text {
				margin-left: 15rpx;
			}
		}

	}

	.new-search-box {
		position: relative;
		width: 77%;
		background-color: #fff;
		border-radius: 33rpx;
		margin-left: 15rpx;
		display: flex;
		align-items: center;
		border: 0.5rpx solid rgb(209, 209, 209);

		.search {
			font-size: 26rpx;
			font-weight: 400;
			color: #999;
		}

		.scan-box {
			width:90rpx;
			margin: 0 15rpx;
			position: absolute;
			right:10rpx;
			display: flex;
			align-items: center;
			justify-content: flex-end;
			z-index: 999;
		}
	}

</style>