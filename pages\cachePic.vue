<template>
	<view v-if="!hasData" style="display:flex;width:100%;height:80vh;justify-content:center;align-items: center;">
		<tn-empty mode="data"></tn-empty>
	</view>
	<view v-else>
		<view class="form-box-col" v-for="(rid,index) in Object.keys(cachePics)" :key="rid">
			<view class="label">{{cachePics[rid].label}}</view>
			<view class="form-box-title" >
				<view style="position: absolute;left:20rpx;">
					<button @tap="goDetail(cachePics[rid],rid)" type=default class="uploadButton">进入表单</button>
				</view>
				<view style="position: absolute;right:20rpx;">
					<button @tap="uploadCachePic(rid,cachePics[rid])" type=default class="uploadButton">一键上传</button>
				</view>
			</view>
			<!-- 显示图片 -->
			<scroll-view scroll-x="true">
				<view style="display: flex;flex-direction: row;">
					<view v-for="(col,idx) in Object.keys(cachePics[rid])" v-if="col!='id'&&col!='label'" style="display: flex;flex-direction: row;">
						<view v-for="(pic,picIdx) in cachePics[rid][col]" style="position: relative;" :key="picIdx">
							<image style="opacity: 0.4;filter: alpha(opacity=40);" :src="pic.filePath" @click="preViewImg(pic.filePath)">
								<view @click="preViewImg(pic.filePath)" class="attach-failed">
									未上传成功
								</view>
							</image>
							<uni-icons type="close" color="#fff" size="20" class="attach-del"
								@click="deleteCachePic(rid, col, picIdx)">
							</uni-icons>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	var checkOpenGPSService = require('../common/util.js').checkOpenGPSService
	export default {
		data() {
			return {
				userId:'',
				cachePics: {},
				hasData: false
			}
		},
		components:{

		},
		methods: {
			goDetail(e,rid){
				let url = 'edit?id=' + e.id + '&rid=' + rid
				uni.navigateTo({
					url:url
				})
			},
			uploadCachePic(rid,e){//以rid为键值的对象
				var that = this
				for(var col in e){
					if(col!='id'&&col!='label'){
						var len=e[col].length
						for(var i=len-1; i>-1; i--){
							this.uploadSend(rid, col, e[col][i], i)
						}
					}
				}
			},
			uploadSend(rid, col, e, idx){
				var that = this
				if (e.url.indexOf('latitude') < 0) {
					if((e.editType=='SGFILE'||e.editType=='SGVIDEO')){
						if(!checkOpenGPSService()){
							this.showErr("请先开启位置服务")
							return
						}
					    if(!getApp().globalData.latitude){
							this.showErr("还未获取到定位信息")
							return
						}
					}
					if(getApp().globalData.latitude){
						e.url += '&latitude=' + getApp().globalData.latitude + '&longitude=' + getApp().globalData.longitude
						this.getAddressName(getApp().globalData.longitude,getApp().globalData.latitude,function(address){
							if(address){
								e.url += '&address=' + address
							}
							that.uploadCacheItem(rid, col, e, idx)
						})
					}
				} else if (e.url.indexOf('address') < 0) {
					let lng = this.getQueryString(e.url, 'longitude')
					let lat = this.getQueryString(e.url, 'latitude')
					this.getAddressName(lng, lat, function(address){
						if(address){
							e.url += '&address=' + address
						}
						that.uploadCacheItem(rid, col, e, idx)
					})
				} else {
					this.uploadCacheItem(rid, col, e, idx)
				}
			},
			getQueryString(url, name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
				var r = url.match(reg);
				if (r != null) return unescape(r[2]);
				return null;
			},
			uploadCacheItem(rid, col, e, idx){
				let that = this
				//#ifdef APP-PLUS||H5
				var header={}
				//#endif
				// #ifdef MP-WEIXIN
				var header={'Cookie': uni.getStorageSync('cookieKey')}
				// #endif
				uni.uploadFile({
					url: e.url, 
					dataType: 'json',
					header: header,
					filePath: e.filePath,
					name: 'upload',
					formData: e.formData,
					success: function(uploadRes) {
						uni.hideLoading()
						if (uploadRes.statusCode != 200) {//上传失败(应用未正常返回)且不是缓存的照片需要缓存
							uni.showModal({
								content: '上传失败:' + uploadRes.errMsg
							})
							return
						} else {
							let res = JSON.parse(uploadRes.data)
							if (res.err) {
								uni.showModal({
									title: '提示',
									showCancel: false,
									content: res.err
								});
								return
							}
							if (res.name && res.name.alert) {
								uni.showModal({
									title: '提示',
									showCancel: false,
									content: res.name.alert
								});
							}
							if (res.state == 1) {
								that.removeCachePic(rid, col, idx)
							} else {//上传失败(应用有返回但返回失败)且不是缓存的照片需要缓存
								uni.showModal({
									content: '上传失败:' + res.err
								})
							}
						}
					},
					fail: function(info) {//上传失败(网络层)且不是缓存的照片需要缓存
						uni.hideLoading()
						uni.showModal({
							content: '上传失败:' + info.errMsg
						})
					}
				})
			},
			deleteCachePic(rid, col, picIdx){
				let that = this
				uni.showModal({
					title:'提示',
					content:'确认要删除离线图片吗?',
					success(c) {
						if(c.confirm){
							that.removeCachePic(rid, col, picIdx)
						}
					}
				})
			},
			removeCachePic(rid, col, picIdx){
				console.log(rid+","+col+","+picIdx)
				this.$delete(this.cachePics[rid][col],picIdx)
				if(this.cachePics[rid][col].length==0){
					delete this.cachePics[rid][col]
				}
				if(Object.keys(this.cachePics[rid]).length==2){
					delete this.cachePics[rid]
				}
				if(Object.keys(this.cachePics).length==0){
					this.hasData = false
				}
				var cachePics={}
				cachePics[this.userId]=this.cachePics
				uni.setStorageSync('cachePics',cachePics)
			},
			preViewImg(url) {
				uni.previewImage({
					current: 0,
					urls: [url]
				});
			},
			preViewMedia(url) {
				var source = [{
					url: url,
					type: "video"
				}]
				wx.previewMedia({
					sources: source,
					current: 0, // 当前显示的资源序号
					url: url // 当前预览资源的url链接
				});
			},
			loadData(){
				getApp().globalData.gpsNeed = 1//开启GPS定位
				this.userId = getApp().globalData.userId
				var cachePics={}
				if(uni.getStorageSync('cachePics')){
					cachePics = uni.getStorageSync('cachePics')
				}
				if(cachePics[this.userId]){
					if(Object.keys(cachePics[this.userId]).length>0){
						this.hasData = true
						this.cachePics=cachePics[this.userId]
					}
				}
			}
		},
		mounted() {
			this.loadData()
			/*缓存的数据格式
			{
			    "21232f297a57a5a743894a0e4a801fc3": {
			        "645b4999c5252": {
			            "id": "demo",
			            "label": "演示页面-故障原因",
			            "COLNAME": [
			                {
			                    "url": "https://dns.arounds.cn:3721/postgre/develop/wxUpload.php?uploadcol=CAMERA&id=demo&rid=645b4999c5252&taskid=&nodeid=1&KEY=5b59c71793ee726f7ff630b2f9bd32576527b4337d29b&master=",
			                    "formData": {},
			                    "filePath": "http://tmp/UhlOZEXORojNc94e875c7cdd73eb32ef6cc20d28400b.png",
								"editType":"SGFILE"//用于判断是否必须定位
			                },
			                {
			                    "url": "https://dns.arounds.cn:3721/postgre/develop/wxUpload.php?uploadcol=CAMERA&id=demo&rid=645b4999c5252&taskid=&nodeid=1&KEY=5b59c71793ee726f7ff630b2f9bd32576527b4337d29b&master=",
			                    "formData": {},
			                    "filePath": "http://tmp/soYHyyqPyZdV9ac23ca2117cbf931280fcdb6e1726d9.png",
								"editType":"SGFILE"//用于判断是否必须定位
			                }
			            ]
			        }
			    }
			}
			*/
		}
	}
</script>
<style>
	page {
		background-color: #f2f1f5;
	}
</style>
<style scoped>
	
	.label {
		width:100%;
		padding:10rpx 0;
		font-weight: 700;
	}
	
	.form-box-col {
		background-color: #fff;
		padding: 20rpx;
		margin:10rpx 0;
		display: block;
		flex-direction: column;
		align-items: center;
		width: 100%;
		font-size: 28rpx;
	}
	
	.form-box-title {
		display:flex;
		height: 70rpx;
		align-items: center;
	}
	
	.uploadButton {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 45rpx;
		font-size: 25rpx;
		border-radius: 30rpx;
		min-width: 150rpx;
		background-color: #EFEFF4;
		border: 1px solid grey;
	}
	
	.attach-failed {
		width:100%;
		left:0;
		top:40%;
		position:absolute;
		text-align:center;
		font-size:30rpx;
		font-weight:700;
	}
	
	.attach-del{
		position:absolute;
		right:5px;
		top:5px;
		background:red;
	}
	
	image,
	video {
		width: 190rpx;
		height: 190rpx;
		margin: 10rpx 10rpx;
		position: relative;
	}
	
</style>