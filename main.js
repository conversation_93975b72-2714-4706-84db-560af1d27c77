
// #ifndef VUE3
import Vue from 'vue'
import App from './App'
//import AMap from './common/amap-wx.js';
//Vue.use(AMap);
//import hnsafe from './static/js/hnsafe.js'
//引入图鸟
import TuniaoUI from 'tuniao-ui'
Vue.use(TuniaoUI)
import store from './store'
let vuexStore = require('@/store/$tn.mixin.js')
var gcj02ToWgs84 = require('./common/util.js').gcj02ToWgs84
var transformLat = require('./common/util.js').transformLat
var transformLon = require('./common/util.js').transformLon
Vue.mixin(vuexStore)
//引入图鸟结束
Vue.config.productionTip = false

App.mpType = 'app'

const app = new Vue({
	store,
    ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import App from './App.vue'

export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif

Vue.prototype.xAjax = function (paraIn,callback,failCallBack) {

	if(getApp().globalData.offline&&!paraIn.login){
		uni.showModal({
			content:"离线模式无法操作"
		})
		return
	}

	if(!paraIn.hideLoading&&!paraIn.hideAll){//hideLoading表示后台运行,不显示状态但要显示报错，hideAll都不显示
		uni.showLoading({
			title: '请稍候...',
			mask: false
		})
	}
	
	if(paraIn.url.substr(0,4).toLowerCase()!='http'){//传入的url不含http的要带上服务器地址
		if(paraIn.url.substr(0,1)!='/'){
			paraIn.url='/'+paraIn.url;
		}
		if(paraIn.url.indexOf('?')!=-1){//app=4用于区分是微信小程序访问
			paraIn.url=paraIn.url.replace('?','?app=4&');
		}else{
			paraIn.url+='?app=4';
		}
		paraIn.url=uni.getStorageSync('currServerUrl') + paraIn.url;
	}
	
	
	uni.request({
		url: paraIn.url,
		header: {
			'Cookie':uni.getStorageSync('cookieKey'),
			'Content-Type': 'application/x-www-form-urlencoded'//POST必须使用该入参,否则服务端接收不到data中的参数
		},
		sslVerify : false,
		method : 'POST',
		timeout: paraIn.timeout||60000,//默认60秒，微信认证需要180秒，因为微信鉴权会出现响应时间130多秒
		data : paraIn.data,
		dataType : 'text',
		success: res => {
			uni.hideLoading()
			if (res.statusCode != 200) {//访问服务器失败
				if(!paraIn.hideAll&&!paraIn.hideErr){
					this.showErr('访问目标文件失败（'+paraIn.url+'）')
				}
				if(paraIn.allwaysCall&&callback){
					callback({'err':true})
				}
				if(failCallBack){
					failCallBack(res)
				}
				return
			}
			if (!res.data) {
				res.data = '{"ok":1}'
			}
			let json = JSON.parse(res.data, function (k, v) {//文本转换成json对象，并且对null转换
				if (v === null) {
					return "";
				}
				return v;
			})
			res.data = json
			if(res.data.sessionId){
				uni.setStorageSync('cookieKey',"PHPSESSID=" + res.data.sessionId)
			}else if(res.header["Set-Cookie"]&&res.header["Set-Cookie"].indexOf("PHPSESSID")>-1){
				//可能的格式:Set-Cookie: QkmefgKpT5HyS=5MPlaSeE9nkRyBYY0fZVa5cenOb1_eRh4v9WQI8tZYHt052YXwKk1awnGxous4jnx82dD0Rx0gB7P8PZXn5WzOa; Path=/; expires=Sun, 06 Mar 2033 12:10:53 GMT; HttpOnly,PHPSESSID=ab9jp1ms483prvabno9s82nnc3; path=/; HttpOnly
				//后端为了安全启用了重新生成sessionid，可能传2个PHPSESSID，先后顺序还不是固定的，需要判断哪个是新的: 
				//"PHPSESSID=okualicl5assfojno8r3ou01p4; path=/,PHPSESSID=2tt6r2vjdmt0kttp90df98nitc; path=/",
				var cookie=res.header["Set-Cookie"].match(/PHPSESSID(\S*);/g)
				var str=cookie[cookie.length-1].match(/PHPSESSID(\S*);/)[1]
				uni.setStorageSync('cookieKey',"PHPSESSID"+str);
			}
			if(res.data.timeout){//非离线模式下才提示退出
				if(!paraIn.hideAll&&!paraIn.hideErr){
					if(!res.data.err){
						res.data.err='未登录或已退出,请重新登录';
					}
					uni.showModal({
						content: res.data.err,
						success: (a) => {
							if (a.confirm) {
								uni.navigateTo({
									url:"login"
								})
							}
						}
					})
				}
				if(paraIn.allwaysCall&&callback){
					callback({'err':true})
				}
			}else if(res.data.err){
				if(!paraIn.hideAll&&!paraIn.hideErr){
					this.showErr(res.data.err)
				}
				if(paraIn.allwaysCall&&callback){
					callback({'err':true})
				}
			}else{
				if(!paraIn.hideAll&&!paraIn.hideMsg){
					if (res.data.msg) {
						uni.showToast({
							title : res.data.msg,
							duration: res.data.duration||3000
						})
					}
					if(res.data.alert){
						uni.showModal({
							content: res.data.alert
						})
					}
				}
				if(callback){
					callback(res.data)
				}
			}
		},
		fail: (err) => {
			uni.hideLoading()
			if(failCallBack){
				failCallBack()
			}
			if(!paraIn.hideNetworkErr && !paraIn.hideAll){
				this.showErr('服务器连接失败:'+JSON.stringify(err)+paraIn.url)
			}
			if(paraIn.allwaysCall&&callback){
				callback({'err':true})
			}
		},
		complete: () => {
			uni.stopPullDownRefresh()
		}
	})
}

Vue.prototype.showErr = function (err) {
	if (err) {
		uni.showModal({
			title  : '错误提示',
			content: JSON.stringify(err)
		})
	}
	//getApp().globalData.notShowErr = false
	uni.hideLoading()
	uni.stopPullDownRefresh()
}
//扫码后的解析
Vue.prototype.scanParse = function (qrcode) {
	var json
	try{
		json=JSON.parse(qrcode)
	}catch(e){
		uni.showModal({
			title:"json格式转换错误:"+qrcode
		})
		return
	}
	if(!json.scanType||!json.url){
		uni.setClipboardData({
			data: qrcode,
			success:function(){
				uni.showModal({
					title: "系统无法识别，已复制到粘贴板:"+qrcode
				})
			}
		})
		return
	}
	this.scanProcess(json.scanType,json.url)
}

Vue.prototype.scanConfirm = function (confirm,scanType,url) {
	var that = this
	if (confirm) { //有扫码后的提示，提示中用[]对要对提示语进行转换
		uni.showModal({
			title: '提示',
			content: confirm,
			success: function(a) {
				if (a.confirm) {
					that.scanProcess(scanType,url)
				}
			}
		})
	} else {
		this.scanProcess(scanType,url)
	}
}

Vue.prototype.scanProcess = function (scanType,url){
	var that = this
	if(scanType==1){//调用外部浏览器
		uni.setClipboardData({
			data: url,
			success:function(){
				uni.showModal({
					title:'您访问的是外部链接，链接地址已复制，请使用浏览器粘贴打开'
				})
			}
		})
	}else if(scanType==2){//内部文件链接，APP直接调用
		uni.navigateTo({
			url: url
		})
	}else if(scanType==3){//服务端URL转换后调用
		if(url.indexOf("?")<0){
			url+="?"
		}
		if(getApp().globalData.latitude){
			url+="&longitude=" + getApp().globalData.longitude + "&latitude=" + getApp().globalData.latitude + "&accuracy="+getApp().globalData.accuracy
			this.getAddressName(getApp().globalData.longitude, getApp().globalData.latitude, function(address){
				if(address){
					url+="&address="+address
				}
				that.scanSendServer(url)
			})
		}else{
			this.scanSendServer(url)
		}
	}else if(scanType==4){//验证用户身份
		uni.login({
			provider: 'weixin',
			success: (res) => {
				this.scanSendServer(url + "?weixinCode=" + res.code)
			},
			fail: (err) => {
				this.content='获取微信码失败:'+ err.errMsg
				this.showModal=true
			}
		})
	}else{
		uni.setClipboardData({
			data: url,
			success:function(){
				uni.showModal({
					title:"系统无法识别，已复制到粘贴板:"+url
				})
			}
		})
	}
}

Vue.prototype.scanSendServer = function (url){
	var that = this
	this.xAjax({
		url:url,
	},function(res){
		if (res.confirm) {
			uni.showModal({
				title: '提示',
				content: res.confirm,
				success: function(a) {
					if (a.confirm) {
						if (res.serverURL) {
							that.xAjax({
								url: res.serverURL
							})
						} else if (res.wxURL) {
							that.scanNavigateTo(res.wxURL)
						}
					}
				}
			})
		} else if (res.wxURL) {
			that.scanNavigateTo(res.wxURL)
		}
	})
}

Vue.prototype.scanNavigateTo = function (url){
	if(typeof url=='string'){//如果是一个wxURL字符串直接本地跳转
		uni.navigateTo({
			url: url
		})
	}else if(typeof url=='object'){//如果是一个数组可能对应多个记录，需要在列表中弹出选项，选中后跳转，只针对列表
		uni.$emit('scanNavigateList', url)
	}
}

// var amapPlugin = new AMap.AMapWX({//初始化高德地图
// 	key: '255f0968e37c9a451d2fcb4e60b3b616'
// })

Vue.prototype.getAddressName = function (lng, lat, callback){
	var lnglat = gcj02ToWgs84(lng,lat)
	let url="https://api.tianditu.gov.cn/geocoder?postStr={'lon':"+lnglat[0]+",'lat':"+lnglat[1]+",'ver':1}&type=geocode&tk=fbadfac86e6fd50f4e502422a5606c2a"
	this.xAjax({
		url:url,
		hideAll:true
	},function(res){
		if(callback){
			if(res.result&&res.result.formatted_address){
				callback(res.result.formatted_address)
			}else{
				callback("")
			}
		}
	},function(){//错误后仍然执行程序
		if(callback){
			callback("")
		}
	})
	//暂停高德，费用5万/年太高
	// amapPlugin.getRegeo({
	// 	location: `${lng},${lat}`,
	// 	success: (data) => {
	// 		if(callback)callback(data[0].name)
	// 	},
	// 	fail: () => {
	// 		if(callback)callback('')
	// 	}
	// })
}