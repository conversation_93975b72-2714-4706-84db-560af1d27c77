<template>
	<view style="min-height:100vh;width:750rpx;">
		<!-- 列表上面的搜索框 -->
		<view style="background-color: #fff;position: sticky;top:0;z-index:20;">
			<view style="background-color: #fff;display:flex;flex-direction:row;width:100%;justify-content:space-between;align-items: center;">
				<uni-search-bar ref="uniSearchBar" style="width:100%;"
					radius="50"
					v-model="keyword"
					focus
					bg-color="#f2f1f5"
					@clickSearch="clickSearch">
				</uni-search-bar>
			</view>
			<uni-segmented-control
				:current="0" 
				:values="['FTA树','故障知识库','厂商知识库','AI大模型知识库']" 
				style-type="text"
				active-color="#007aff"
				@clickItem="onClickSegmented" 
			/>
		</view>
		<view>
			<view v-if="listData.length==0" style="display:flex;width:100%;height:80vh;justify-content:center;align-items: center;">
				<tn-empty mode="data"></tn-empty>
			</view>
			<view v-for="(item,index) in listData" :key="item.id" :data-index="item.id">
				<view class="list-cell" @click="goDetail(item.id)">
					<scroll-view scroll-x=true>
						<rich-text :nodes="item.data" :selectable="true" style="padding: 10rpx 0;"></rich-text>
					</scroll-view>
				</view>
			</view>
		</view>
		<uni-popup ref="popup" @close="closePopup" type="bottom">
			<view style="height: 900rpx;background-color: #fff;width:750rpx;padding: 20rpx;">
				<rich-text :nodes="detail" class="selectable-text"></rich-text>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				keyword:'',
				currentSegmented:0,
				listData:[],
				detail:""
			}
		},
		methods: {
			onClickSegmented(e){
				this.currentSegmented=e.currentIndex
				let cat = this.getCat(e.currentIndex)
				this.searchKnowledge(cat)
			},
			clickSearch(){
				let cat = this.getCat(this.currentSegmented)
				this.searchKnowledge(cat)
			},
			getCat(id){
				if(id==0){
					return 'FTA'
				}
				if(id==1){
					return 'FAULT'
				}
				if(id==2){
					return 'SUPPORT'
				}
				if(id==3){
					return 'COMMON'
				}
			},
			goDetail(id){
				let that = this
				let url = 'interface/ITSM/getKnowledgeById.php?id=' + id
				this.xAjax({
					url: url
				}, function(res) {
					if (res.err) {
						return
					}
					that.detail = res
					that.$refs.popup.open()
				})
			},
			closePopup(){//关闭签名弹窗
				this.$refs.popup.close()
			},
			pullDownToRefresh(filter) {
				let cat = this.getCat(this.currentSegmented)
				this.searchKnowledge(cat)
			},
			searchKnowledge(cat){
				let that = this
				let url = 'interface/ITSM/searchKnowledgeApp.php?cat=' + cat + '&keyword=' + this.keyword
				this.xAjax({
					url: url
				}, function(res) {
					if (res.err) {
						return
					}
					that.listData=res
				})
			},
			onLongPress(e) {
			  // 获取触发事件的rich-text内容
			  const richText = e.target;
			  // 获取用户长按时的位置
			  const x = e.touches[0].pageX;
			  const y = e.touches[0].pageY;
			  // 创建一个临时的textarea元素用于复制文本
			  const textarea = document.createElement('textarea');
			  textarea.value = richText.innerText;
			  textarea.style.position = 'absolute';
			  textarea.style.left = `${x}px`; // 设置到长按位置
			  textarea.style.top = `${y}px`;
			  textarea.style.opacity = '0';
			  // 将textarea添加到body
			  document.body.appendChild(textarea);
			  // 选中临时textarea的内容
			  textarea.select();
			  // 复制选中的内容到剪贴板
			  document.execCommand('copy');
			  // 移除临时textarea元素
			  document.body.removeChild(textarea);
			  uni.showToast({
				title: '复制成功',
				icon: 'success',
				duration: 2000
			  });
			}
		},
		onLoad(e){
			let filter=decodeURIComponent(e.filter.split("-")[1])
			let keyword=filter.split("=")
			this.keyword=keyword[1]
			this.searchKnowledge('FTA');
		},
		onReady() {

		},
		onPullDownRefresh() {
		    this.pullDownToRefresh()
		}
	}
</script>

<style lang="scss">
	
	page {
		min-height: 100%;
		display: flex;
		align-items: center;
		background-color: #f2f1f5;
	}
	
	view {
		line-height: inherit
	}
	
	.selectable-text {
		user-select: text; /* 允许文本被选中 */
	}
	
	.rich-text-container {
		user-select: none; /* 禁止选择文本 */
		-webkit-user-drag: none; /* 禁止拖动文本 */
	}
	
	.main-top {
		position: sticky;
		left: 0;
		top: 0;
		z-index: 999;
		width: 100%;
		background-color: #fafbfc;
	}
	
	.list-cell{
		position: relative;
		width: 710rpx;
		margin: 20rpx;
		padding: 20rpx;
		border-radius: 20rpx;
		background-color: #fff;
	}
	//缓存按钮
	.cache{
		position:absolute;
		top:40rpx;
		right:0;
		margin-right:-5rpx;
		padding:10rpx 0 0 10rpx;
	}
	// 删除按钮
	.trash{
		position:absolute;
		bottom:30rpx;
		right:0;
		margin-right:-5rpx;
		padding:0 0 10rpx 10rpx;
	}
	.list-title{
		color: #000;
		//padding-right:30rpx;
		margin-bottom:15rpx;
		font-size: 35rpx;
		font-weight: bolder;
		width:600rpx;
		overflow:hidden;//省略号显示必须三个配合使用,white-space: nowrap;在后台配置动态加载
		text-overflow:ellipsis;
	}
	.list-content {
		display: flex;
		flex-direction: row;
		font-size: 28rpx;
		line-height: 45rpx;
		color: #777;
		overflow: hidden;
		&-RT{
			position: absolute;
			//margin-left:30rpx;
			right:0;
			top:0;
		}
		&-RB{
			position: absolute;
			right:0;
			bottom:0;
		}
		&-MB{
			position: absolute;
			left:280rpx;
			bottom:0;
		}
	}
	.record-button-layout{
		display: flex;
		flex-direction: row;
		font-size: 25rpx;
		width:100%;
		background-color: #fff;
		z-index: 5;
		flex-wrap: nowrap;
		justify-content: space-around;
		.record-button{
			background-color:#409EFF;
			color:#fff;
			width:100%;
			margin:10rpx;
			padding:0 !important;//按钮默认padding左右为14,容易引起跨行
			border-radius: 10rpx;
		}
	}
	//左下角的tag
	.lb-tag{
		border-radius:50%;
		color:white;
		background-color: green;
		width:45rpx;
		font-size: 22rpx;
		margin-right:15rpx;
		justify-content: center;
	}
	
	//抽屉式弹窗搜索栏
	.fttx-form-group {
		background-color: #fff;
		padding: 0;
		display: flex;
		align-items: center;
		height:90rpx;
		margin-right:20rpx;
	}
	
	.fttx-form-group .title {
		width: 180rpx;
		color: #303133;
		display: flex;
		align-items: center;
		margin-left:0rpx;
		padding-left:0rpx;
		padding-right: 20rpx;
		font-size: 27rpx;
		position: relative;
		height: 60rpx;
		font-weight: 700;
	}
	
	.fttx-form-group input {
		flex: 1;
		font-size: 25rpx;
		color: #555;
		height: 54rpx;
		padding-left: 27rpx;
		width:100%;
		margin-right:0;
		border-radius:27rpx ;
		background-color: #f2f1f5;
	}

	.btnContainer {
		display: flex;
		align-items: center;
		width:100%;
		flex: 1;
		flex-direction: row;
		position: fixed;
		bottom:0;
		background-color: #FFF;
		border-bottom-left-radius: 25rpx;
		height: 90rpx;
	}
	
	.optionContainer {
		display: flex;
		align-items: center;
		width:100%;
		flex: 1;
		flex-direction: row;
		flex-wrap: wrap;
	}
	
	.optionBtn {
		padding: 0rpx 2rpx;
		margin: 0 10rpx 20rpx 10rpx;
		line-height: 50rpx;
		height: 50rpx;
		font-size: 22rpx;
		border-radius: 25rpx;
		width:185rpx;
		background-color: #f2f1f5;
		border: 0.5px solid #f2f1f5;
		text-overflow: ellipsis;
		font-weight: 500;
	}
	
	.optionSelect {
		padding: 0rpx 2rpx;
		margin: 0 10rpx 20rpx 10rpx;
		line-height: 50rpx;
		height: 50rpx;
		font-size: 22rpx;
		border-radius: 25rpx;
		width:185rpx;
		background-color: #d9ecff;
		border:0.5px solid #3688ff;
		// color:#409EFF;
		color:#3688ff;
		text-overflow: ellipsis;
		font-weight: 500;
	}
	
	.search-scan{
		position: absolute;
		right: 110rpx;
	}
	
	.searchBtn {
		padding: 0 5px;
		height: 70rpx;
		font-size: 28rpx;
		border-radius: 50rpx;
		width:240rpx;
	}

	.option-filter {
		margin:0 50rpx 10rpx 10rpx;
		background-color:#fff;//#f6f5f9;
		border: 0.5rpx solid #aaa;
		width:100%;
		height:48rpx;
		border-radius: 27rpx;
		padding-left: 20rpx;
	}
</style>
