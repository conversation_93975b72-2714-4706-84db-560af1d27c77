<template>
	<text :class="classes" :style="'font-size:'+ size +'px;color:'+ color+';transform: rotate('+(rotate||'0')+'deg);'">
	</text>
</template>

<script>
	import './MaterialDesign-Webfont/scss/materialdesignicons.scss'
	export default {
		name:"mdi-icon",
		props:{
			icon:{
				type:String,
				required:true
			},
			size:{
				type:String,
				required:true
			},
			color:{
				type:String,
				required:true
			},
			rotate:{
				type:[String]
			}
			// flip:{
			// 	type:String,
			// 	validator(value){
			// 		switch (value+""){
			// 			case 'horizontal' :
			// 			case 'vertical' :
			// 				return true
			// 			default:
			// 				return false
			// 		}
			// 	}
			// }
		},
		computed:{
			classes(){
				var classes = ['mdi-icon','mdi',this.icon];
				//if(this.rotate) classes.push('mdi-rotate-'+this.rotate);
				//else if(this.flip) classes.push('mdi-flip-'+this.flip.slice(0,1));
				return classes
			}
		}
	}
</script>

<style lang="scss" scoped>
	@import './MaterialDesign-Webfont/scss/materialdesignicons.scss';
	.mdi-icon{
		display: inline-flex;
		width: 1em;
		height: 1em;
		justify-content: center;
		align-items: center;
	}
</style>
