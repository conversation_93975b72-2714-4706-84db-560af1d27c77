## 1.3.0（2022-06-28）
1. [修改] - 之前使用 canvas 在页面上展示画面，改为 video 标签在页面展示视频流画面。
2. [增强] - 现在只获取扫描框中的画面进行二维码识别，这样可有效的节省资源与提高识别效率。
3. [增强] - 提升调用摄像头的分辨率。
4. [增强] - 提升识别大二维码的能力，之前在低端机上识别大二维码容易识别不到。
5. [新增] - 新增错误事件返回，返回内部报错。错误说明请查看文档。
## 1.2.1（2022-06-01）

新增闪光灯开关(只有在谷歌内核浏览器中显示，推荐微信内置浏览器)

新增https环境检测(总有人不看介绍，直接使用导致无法正常调用摄像头。)

更新文档
## 1.2.0（2022-06-01）
新增闪光灯开关(只有在谷歌内核浏览器中显示，推荐微信内置浏览器)

新增https环境检测(总有人不看介绍，直接使用导致无法正常调用摄像头。)
## 1.1.0（2022-01-25）
1.支持全屏 - 
2.支持前置摄像头 - 
3.支持高清调用摄像头 - 
## 1.0.4（2021-12-24）
更新插件说明
## 1.0.3（2021-12-21）
希望大家多去我小程序中逛一些，就当支持作者了。谢谢大家，底下有小程序二维码。
## 1.0.2（2021-12-21）
更新文档
## 1.0.1（2021-12-21）
正式上线插件
## 1.0.0（2021-12-21）
1.0.0
