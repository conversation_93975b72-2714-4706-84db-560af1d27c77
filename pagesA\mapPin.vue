<template>
	<view class="container">
		<!-- 地图组件 -->
		<map id="map" class="map" :latitude="latitude" :longitude="longitude" :markers="markers" :polyline="polyline"
			:circles="circles" :scale="scale" :enable-satellite="satellite" @tap="mapTapHandler" @markertap="markerTap"
			show-location @regionchange="regionChange" @callouttap="callOutTap"></map>

		<!-- 控制按钮 -->
		<view class="control-btn top-right" style="right: 160rpx;" @click="togglePanel('layer')" v-if="showPinBtn">
			<mdi-icon icon="mdi-layers" size="25" rotate="30" />
		</view>
		<view class="control-btn top-right" @click="togglePanel('pin')" v-if="showPinBtn">
			<mdi-icon icon="mdi-pin" size="25" rotate="30" />
		</view>

		<view v-if="currentPanel=='layer'" class="panel">
			<view class="panel-content">
				<!-- 三列布局 -->
				<view class="">
					点类
				</view>
				<view class="panel-detail">
					<view v-for="(p, index) in Object.keys(pointTypes)"
						v-if="pointTypes[p].LAYER=='1'&&pointTypes[p].ZOOMS.split(',')[0]<=zoom&&pointTypes[p].ZOOMS.split(',')[1]>=zoom"
						:key="index" class="btn-item" @click="handleSelectLayer('point',p)"
						:style="{background:pointTypes[p].DEFAULT_SELECT=='1'?'#9FC4FD':'#F8F9FA'}">
						<image class="icon" :src="currentIconUrl+ pointTypes[p].ICON + '.svg'"></image>
						<text class="text">{{ pointTypes[p].LAYER_NAME }}</text>
					</view>
				</view>
				<view class="">
					线类
				</view>
				<view class="panel-detail">
					<view v-for="(l, index) in Object.keys(lineTypes)"
						v-if="lineTypes[l].LAYER=='1'&&lineTypes[l].ZOOMS.split(',')[0]<=zoom&&lineTypes[l].ZOOMS.split(',')[1]>=zoom"
						:key="index" class="btn-item" @click="handleSelectLayer('line',l)"
						:style="{background:lineTypes[l].DEFAULT_SELECT=='1'?'#9FC4FD':'#F8F9FA'}">
						<view style="height: 4rpx;width: 80%;margin-bottom: 14rpx;"
							:style="{background:lineTypes[l].ST_COLOR}">
						</view>
						<text class="text">{{ lineTypes[l].LAYER_NAME }}</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 展开面板 -->
		<view v-if="currentPanel=='pin'" class="panel">
			<view class="panel-content">
				<!-- 三列布局 -->
				<view class="panel-detail">
					<view v-for="(item, index) in Object.keys(pointTypes)" v-if="pointTypes[item].PIN==1" :key="index"
						class="btn-item" @click="handleClick(item)">
						<image class="icon" :src="currentIconUrl+ pointTypes[item].ICON + '.svg'"></image>
						<text class="text">{{ pointTypes[item].LAYER_NAME }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 右下角操作按钮组 -->
		<view class="button-group">
			<!-- 定位按钮 -->
			<view class="action-btn" @click="moveToLocation">
				<mdi-icon icon="mdi-map-marker-radius" size="25" />
			</view>

			<!-- 地图类型切换 -->
			<view class="action-btn" @click="switchMapType">
				<mdi-icon icon="mdi-satellite-variant" size="25" />
			</view>
		</view>

		<!-- 固定在底部的双按钮容器 -->
		<view class="pin-button-container" v-if="startPin">
			<!-- 打点按钮 -->
			<view class="pin-button mark-button" @click="handlePinPoint">
				<uni-icons type="location-filled" size="20" color="#fff"></uni-icons>
				<text class="pin-button-text">打点</text>
			</view>

			<!-- 回退按钮 -->
			<view class="pin-button cancel-button" @click="handleCancelPoint">
				<mdi-icon icon="mdi-arrow-u-left-top" size="20" color="#fff" />
				<text class="pin-button-text">回退</text>
			</view>

			<!-- 完成按钮 -->
			<view class="pin-button confirm-button" @click="handleMarkConfirm">
				<uni-icons type="checkmarkempty" size="20" color="#fff"></uni-icons>
				<text class="pin-button-text">完成</text>
			</view>
		</view>
		<uni-popup ref="popup" background-color="#fff" type="center" border-radius="20px 20px 20rpx 20rpx">
			<view class="" style="position:relative; padding: 20rpx;width: 600rpx;display: flex;align-items: center;flex-direction: column;">
				<radio-group @change="addTypeChange" style="width: 100%; display: flex;flex-direction: row;justify-content: space-evenly;">
					<label>本次新建资源 <radio :value="0" :checked="addNewType==0"/></label>
					<label>绑定现网资源 <radio :value="1" :checked="addNewType==1"/></label>
				</radio-group>
				<view class="" v-if="addNewType==0" style="width:90%">
					<input type="text" v-model="deviceName" style="margin: 20rpx 0rpx;border:1px solid #aaa"/>
				</view>
				<view class="" v-if="addNewType==1" style="width:90%">
					<input type="text" v-model="bindName" style="margin: 20rpx 0rpx;border:1px solid #aaa" @input="getDeviceByKeyword" @blur="showDropdown=false"/>
				</view>
				<!--存量资源选项-->
				<scroll-view v-if="showDropdown && suggestions.length > 0" class="dropdown" scroll-y>
					<view v-for="(item, index) in suggestions" :key="index" class="dropdown-item" @tap="selectOption(item)">
						{{ item.value }}
					</view>
				</scroll-view>
				<!--确认按钮-->
				<view style="width: 100%; display: flex;flex-direction: row;justify-content: space-evenly;">
					<button type="primary" size="mini" @click="confirmAdd">确认</button>
					<button type="warn" size="mini" @click="cancelAdd">取消</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import mdiIcon from 'pagesA/components/mdi-icon/components/mdi-icon/mdi-icon.vue'
	import AMap from '../common/amap-wx.js'
	var myAmap = new AMap.AMapWX({ //初始化高德地图
		key: '8a9caeab79370d62ef5301f6d94e3d03'
	})
	export default {
		components: {
			mdiIcon
		},
		data() {
			return {
				scale: 3.6, //缩放比例
				longitude: 103.14,
				latitude: 35.49,
				markers: [], // 地图上的标记点的数组
				polyline: [],
				circles: [],
				currentPanel: '',
				satellite: false, // 0-标准 1-卫星
				layers: [],
				gpsInterval: null,
				url: '',
				deviceName: '',
				pinLayer: '',
				startPin: false,
				showPinBtn: true,
				showContextMenu: false,
				showDelete: false,
				tapMarker: {},
				addNewType: 0,
				lineTypes: {},
				pointTypes: {},
				zoom: 0,
				isMarkerTap: false,
				currentIconUrl: '',
				suggestions: [],
				showDropdown: false,
				bindName: ''
			}
		},
		methods: {
			togglePanel(panel) {
				if (panel == this.currentPanel) {
					this.currentPanel = ''
				} else {
					this.currentPanel = panel
				}
				this.showContextMenu = false
			},
			handleSelectLayer(type, layer) {
				if (type == 'line') {
					this.lineTypes[layer].DEFAULT_SELECT = this.lineTypes[layer].DEFAULT_SELECT == '1' ? '0' : '1'
				}
				if (type == 'point') {
					this.pointTypes[layer].DEFAULT_SELECT = this.pointTypes[layer].DEFAULT_SELECT == '1' ? '0' : '1'
				}
				this.filterLayers()
			},
			selectOption(item) {
				if (item.id!='`') {
					this.bindName = item.value
					this.bindId = item.id
				} else {
					this.bindName = ''
				}
				this.showDropdown = false
			},
			getDeviceByKeyword(e) {
				this.xAjax({
					url: this.url+"&file=getDeviceByKeyword&layerid=" + this.pinLayer + "&filter[value]="+e.detail.value
				},(res) => {
					this.showDropdown = true
					this.suggestions = res
				})
			},
			filterLayers() {
				let tempMarkers = []
				for (let p of this.orMarkers) {
					if (this.pointTypes[p.data.LAYER_ID].DEFAULT_SELECT == '1' && this.pointTypes[p.data.LAYER_ID].ZOOMS
						.split(',')[0] <= this.zoom && this.pointTypes[p.data.LAYER_ID].ZOOMS.split(',')[1] >= this.zoom) {
						tempMarkers.push(p)
					}
				}
				this.markers = tempMarkers
				let tempPolyine = []
				for (let l of this.orPolyline) {
					if (this.lineTypes[l.data.LAYER_ID].DEFAULT_SELECT == '1' && this.lineTypes[l.data.LAYER_ID].ZOOMS
						.split(',')[0] <= this.zoom && this.lineTypes[l.data.LAYER_ID].ZOOMS.split(',')[1] >= this.zoom) {
						tempPolyine.push(l)
					}
				}
				this.polyline = tempPolyine
			},
			addTypeChange(e){
				this.addNewType = e.detail.value
			},
			confirmAdd(){
				if(this.addNewType == 0 && !this.deviceName) {
					uni.showToast({
						title:'请填写名称',
						icon:'none',
					})
					return
				}
				if(this.addNewType == 1 && !this.bindId) {
					uni.showToast({
						title:'请选择存量资源',
						icon:'none',
					})
					return
				}
				let lng = getApp().globalData.longitude
				let lat = getApp().globalData.latitude
				myAmap.getRegeo({
					location: `${lng},${lat}`, // 格式：经度,纬度
					success: (res) => {
						const result = res[0]; // 第一个结果
						if (result) {
							this.addNewDeviceByData({
								layerId: this.pinLayer,
								geometry: 'Point',
								deviceRid: this.bindId,
								deviceName: this.deviceName,
								lng: lng,
								lat: lat,
								coordinates: JSON.stringify([lng, lat]),
								buildId: this.rid,
								distance: 0,
								address: result.name,
								pin: 1
							})
						}
					},
					fail: (error) => {
						console.error('逆地理编码失败:', error);
					}
				})
			},
			addNewDeviceByData(data,callback) {
				this.xAjax({
					url: this.url + "&file=addNewDevice",
					data: data,
				}, (res) => {
					if(callback){
						callback()
					}
					let f = this.initFeaturesFromData(res)[0]
					console.log(JSON.stringify(this.markers))
					this.$refs.popup.close()
					if (this.continuousFlag) {
						this.continuousPoints.push({
							point: f
						})
						if (this.continuousPoints.length > 1) {
							let tempPath = [
								[this.continuousPoints[this.continuousPoints.length - 2].point.data.LNG, this
									.continuousPoints[this.continuousPoints.length - 2].point.data.LAT
								],
								[f.data.LNG, f.data.LAT]
							]
							this.xAjax({
								url: this.url + "&file=addNewDevice",
								data: {
									layerId: this.pointTypes[this.pinObj]['AUTO_LINK_LINE'],
									geometry: 'LineString',
									coordinates: JSON.stringify(tempPath),
									deviceName: this.continuousPoints[this.continuousPoints.length - 2]
										.point.data.NAME + '__' + f.data.NAME,
									buildId: this.rid,
									points: this.continuousPoints[this.continuousPoints.length - 2].point
										.data.RID + ',' + f.data.RID,
									distance: this.getDistance(tempPath[0][0], tempPath[0][1], tempPath[1][
										0
									], tempPath[1][1])
								}
							}, (res) => {
								let line = this.initFeaturesFromData(res)[0]
								this.continuousPoints[this.continuousPoints.length - 1].line = line
							})
						}
					}
				})
			},
			cancelAdd() {
				this.$refs.popup.close()
			},
			getDistance(lon1, lat1, lon2, lat2) {
				const R = 6371e3; // 地球半径，单位米
				const φ1 = lat1 * Math.PI / 180;
				const φ2 = lat2 * Math.PI / 180;
				const Δφ = (lat2 - lat1) * Math.PI / 180;
				const Δλ = (lon2 - lon1) * Math.PI / 180;

				const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
					Math.cos(φ1) * Math.cos(φ2) *
					Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
				const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

				return R * c; // 返回距离，单位米
			},
			markerTap(e) {
				const markerId = e.detail.markerId;
				let tapMarker = this.markers.find(m => m.id === markerId);
				let f = tapMarker.data
				let itemList = []
				if(f.DEVICE != 1) {
					itemList.push('详情')
					if (f.STATUS == 1 || f.STATUS == 2) {
						itemList.push('删除')
					}
					if (this.pointTypes[f.LAYER_ID].ALLOW_EDIT == 1) {
						itemList.push('移动')
					}
				}
				if (f.DEVICE == 1) {
					itemList.push('打点')
				}
				uni.showActionSheet({
					title: this.pointTypes[f.LAYER_ID].LAYER_NAME+' '+ f.NAME,
					itemList: itemList,
					success: (res) => {
						if (itemList[res.tapIndex] == '详情') {
							uni.navigateTo({
								url: "/pages/edit?id=mapFeaturePoint&rid=" + f.RID
							})
						}
						if (itemList[res.tapIndex] == '删除') {
							uni.showModal({
								title: '提示',
								content: '确认要删除' + f.NAME,
								success: (res) => {
									if (res.confirm) {
										this.deleteFeature(f,()=>{
											this.xAjax({
												url: this.url + "&file=bindDevice",
												data: {
													rid: f.ZY_LINKID,
													status: 0,
												}
											},(res)=>{
												
											})
										})
									}
								},
							})
						}
						if (itemList[res.tapIndex] == '移动') {
							this.movePoint(f)
						}
						if (itemList[res.tapIndex] == '打点') {
							if(!this.moveToLocation()){
								return
							}
							let lng = getApp().globalData.longitude
							let lat = getApp().globalData.latitude
							myAmap.getRegeo({
								location: `${lng},${lat}`, // 格式：经度,纬度
								success: (res) => {
									const result = res[0]; // 第一个结果
									if (result) {
										let data = {
											layerId: f.LAYER_ID,
											geometry: 'Point',
											deviceRid: f.RID,
											deviceName: f.NAME,
											lng: lng,
											lat: lat,
											coordinates: JSON.stringify([lng, lat]),
											buildId: this.rid,
											distance: 0,
											address: result.name,
											pin: 1,
											type: 'zy'
										}
										this.addNewDeviceByData(data,()=>{
											this.xAjax({
												url: this.url + "&file=bindDevice",
												data: {
													rid: f.RID,
													status: 1,
												}
											},(res)=>{
												if(res.ok){
													this.markers = this.markers.filter(m=>m.data.RID != f.RID)
												}
											})
										})
									}
								},
								fail: (error) => {
									console.error('逆地理编码失败:', error);
								}
							})
							
						}
					},
					fail: (res) => {
						console.log(res.errMsg);
					}
				});

			},
			movePoint(f) {
				this.circles = [{
					latitude: f.ORIGINAL_LAT,
					longitude: f.ORIGINAL_LNG,
					color: '#C4E1F94D',
					fillColor: "#b3deff64",
					strokeWidth: 1,
					radius: parseFloat(this.pointTypes[f.LAYER_ID].PIN_MOVE_RADIUS || 10)
				}]
				this.movingPoint = f
			},
			deleteFeature(f, callback) {
				this.xAjax({
					url: this.url + "&file=deleteDevice",
					data: {
						rid: f.RID,
						geometry: f.GEOMETRY,
						buildId: this.rid
					}
				}, (res) => {
					if (res.ok) {
						if (callback) {
							callback()
						}
						if (f.GEOMETRY == 'Point') {
							for (let i = 0; i < this.markers.length; i++) {
								if (this.markers[i].data.RID == f.RID) {
									this.markers.splice(i, 1)
									break
								}
							}
							this.polyline = this.polyline.filter((item) => !item.data.POINTS.split(',').includes(f
								.RID));
						}
						if (f.GEOMETRY == 'LineString') {
							for (let i = 0; i < this.polyline.length; i++) {
								if (this.polyline[i].data.RID == f.RID) {
									this.polyline.splice(i, 1)
									break
								}
							}
						}
					}
				})
			},
			handleClick(item) { //点击面板上的图标开始打点
				this.pinLayer = item
				this.currentPanel = ''
				if (this.pointTypes[this.pinLayer]['AUTO_LINK_LINE']) {
					this.continuousFlag = true;
					this.continuousPoints = [];
					this.startPin = true
					this.showPinBtn = false
				}
				this.alertAddNewDevice()
			},
			alertAddNewDevice(lng,lat){
				if(!this.moveToLocation()){
					return
				}
				this.bindId = ''
				this.bindName = ''
				let deviceName = this.deviceName.split("#")
				if (deviceName.length>1) {
					let idx = deviceName[deviceName.length-1]
					if (idx.trim()!=""&&!isNaN(idx)) {
						deviceName[deviceName.length-1] = (parseInt(idx)+1)
					} else {
						deviceName[deviceName.length-1] = 1
					}
				} else {
					deviceName[1] = 1
				}
				this.deviceName = deviceName.join("#")
				this.$refs.popup.open('center')
			},
			handlePinPoint() {
				if (getApp().globalData.locationError != '') {
					this.showErr(getApp().globalData.locationError)
					return
				}
				if (!getApp().globalData.latitude) {
					this.showErr("还未定位")
					return
				}
				this.alertAddNewDevice()
			},
			handleCancelPoint() { //点回退按钮删除点
				if (this.continuousPoints && this.continuousPoints.length === 0) {
					uni.showToast({
						title: '没有点位不能回退',
						icon: 'none'
					})
					return
				}
				uni.showModal({
					title: '提示',
					content: '是否回退到上一个点位?',
					success: (res) => {
						if (res.confirm) {
							let p = this.continuousPoints[this.continuousPoints.length - 1];
							if (p.line) {
								this.deleteFeature(p.line.data, () => {
									this.deleteFeature(p.point.data, () => {
										this.continuousPoints.pop();
									})
								})
							} else {
								this.deleteFeature(p.point.data, () => {
									this.continuousPoints.pop();
								})
							}
						}
					}
				})
			},
			handleMarkConfirm() {
				this.startPin = false
				this.showPinBtn = true
			},
			base36ToDecimal(str36) {
				// 定义36进制字符对应的值
				const charMap = {};
				for (let i = 0; i < 10; i++) charMap[i.toString()] = i;
				for (let i = 0; i < 26; i++) charMap[String.fromCharCode(65 + i)] = 10 + i;

				// 转换为大写统一处理
				const upperStr = str36.toUpperCase();
				let decimal = 0;

				// 从右到左处理每一位
				for (let i = 0; i < upperStr.length; i++) {
					const char = upperStr[upperStr.length - 1 - i];
					if (!(char in charMap)) {
						throw new Error(`无效的36进制字符: ${char}`);
					}
					decimal += charMap[char] * Math.pow(36, i);
				}

				return decimal;
			},
			// 新增地图切换方法
			switchMapType() {
				this.satellite = this.satellite === true ? false : true
				// 如果需要保持与地图交互状态，可以在这里添加逻辑
			},
			// 移动地图到指定位置
			moveToLocation() {
				if (getApp().globalData.locationError != '') {
					this.showErr(getApp().globalData.locationError)
					return false
				}
				if (!getApp().globalData.latitude) {
					this.showErr("还未定位")
					return false
				}
				const mapContext = uni.createMapContext('map', this)
				mapContext.moveToLocation({
					latitude: getApp().globalData.latitude,
					longitude: getApp().globalData.longitude
				})
				return true
			},
			mapTapHandler(e) { //点击地图后隐藏
				if (this.currentPanel) {
					this.currentPanel = ''
				}
				this.showContextMenu = false
				if (this.movingPoint) {
					let f = this.movingPoint
					let lng = e.detail.longitude
					let lat = e.detail.latitude
					if (f.ORIGINAL_LNG && this.getDistance(f.ORIGINAL_LNG, f.ORIGINAL_LAT, lng, lat) > parseFloat(this
							.pointTypes[f.LAYER_ID].PIN_MOVE_RADIUS || 10)) {
						uni.showToast({
							title: `移动距离不能超过${this.pointTypes[f.LAYER_ID].PIN_MOVE_RADIUS || 10}米`,
							icon: 'none'
						})
						this.movingPoint = null
						this.circles = []
					} else {
						this.xAjax({
							url: this.url + '&file=updateDevice',
							data: {
								lng: lng,
								lat: lat,
								geometry: 'Point',
								coordinates: JSON.stringify([lng, lat]),
								rid: f.RID,
							},
						}, (res) => {
							if (res.ok) {
								f.LNG = lng;
								f.LAT = lat;
								f.COORDINATES = JSON.stringify([lng, lat]);
								this.initFeaturesFromData(f)
								let lines = this.initFeaturesFromData(res.data)
								this.movingPoint = null
								this.circles = []
							}
						})
					}
				}
			},
			regionChange(e) {
				if (e.type == "end") {
					this.mapContext.getScale({
						success: (res) => {
							this.zoom = res.scale;
						}
					});
					let url = this.url
					url += "&file=getAllDeviceByRect&northWest=" + e.detail.region.southwest.longitude + ',' + e.detail
						.region.northeast.latitude + "&southEast=" + e.detail.region.northeast.longitude + "," + e.detail
						.region.southwest.latitude
					url +=
						'&layerids=well,pole,zcd,hole,jz,gj,gw1,gw2,gjt,gf,gz,qqy,yhd,zjfiber,zgfiber,pxfiber,pipeline,poleline,trench,fiber'
					this.xAjax({
						url: url,
						hideLoading: true
					}, (res) => {
						url = this.url
						url += "&file=getZyDeviceByRect&northWest=" + e.detail.region.southwest.longitude + ',' + e.detail
							.region.northeast.latitude + "&southEast=" + e.detail.region.northeast.longitude + "," + e.detail
							.region.southwest.latitude
						this.xAjax({
							url: url,
							hideLoading: true
						}, (res1) => {
							this.markers = []
							this.polyline = []
							this.orMarkers = this.initFeaturesFromData(res.point)
							this.orPolyline = this.initFeaturesFromData(res.line)
							this.filterLayers()
							this.initFeaturesFromData(res1.data)
						})	
					})
					
				}
			},
			getIdxOfFeatures(f) {
				if (f.data.GEOMETRY == 'Point') {
					let i = 0
					for (let m of this.markers) {
						if (m.data.RID == f.data.RID) {
							return i
						}
						i++
					}
				}
				if (f.data.GEOMETRY == 'LineString') {
					let i = 0
					for (let l of this.polyline) {
						if (l.data.RID == f.data.RID) {
							return i
						}
						i++
					}
				}
				return -1
			},
			initFeaturesFromData(data, cover = true) {
				data = Array.isArray(data) ? data : [data];
				let features = [];
				for (let i = 0; i < data.length; i++) {
					let f = data[i]
					let feature = null
					if (f.GEOMETRY === 'Point') {
						let iconPath = f.STATUS == 1 ? this.pointTypes[f.LAYER_ID].ICON_BUILDING : ((f.ORIGINAL_LAT && f
							.ORIGINAL_LAT != f.LAT && f.ORIGINAL_LNG != f.LNG) ? (this.pointTypes[f.LAYER_ID]
							.ICON + '_m') : this.pointTypes[f.LAYER_ID].ICON)
						if(f.DEVICE == 1){
							iconPath = this.pointTypes[f.LAYER_ID].ICON + '_m'
							if(f.STATUS != 0){
								continue
							}
						}
						let size = parseFloat(this.pointTypes[f.LAYER_ID].SIZE) || 20
						feature = {
							id: this.base36ToDecimal(f.RID),
							latitude: f.LAT,
							longitude: f.LNG,
							zIndex: parseFloat(this.pointTypes[f.LAYER_ID].Z_INDEX ?? 10),
							iconPath: this.currentIconUrl + iconPath + '.png',
							width: parseInt(size * (this.pointTypes[f.LAYER_ID].ICON_RATIO || 1)),
							height: size,
							data: f,
							anchor: {
								x: 0.5,
								y: 0.5
							},
							callout: {
								content: '地址:' + (f.ADDRESS || '') + "\n" + '名称:' +this.pointTypes[f.LAYER_ID].LAYER_NAME+' '+ f.NAME,
								color: '#fff',
								fontSize: 14,
								bgColor: '#00c16f',
								textAlign: 'center',
								padding: 8,
								borderRadius: 5,
							}
						}
						let idx = this.getIdxOfFeatures(feature)
						if (idx == -1) {
							this.markers.push(feature)
						} else if (cover) {
							this.markers.splice(idx, 1, feature)
						}
					}
					if (f.GEOMETRY === 'LineString') {
						let path = []
						for (let p of JSON.parse(f.COORDINATES)) {
							path.push({
								latitude: p[1],
								longitude: p[0]
							})
						}
						feature = {
							points: path,
							color: this.lineTypes[f.LAYER_ID].ST_COLOR,
							width: parseFloat(this.lineTypes[f.LAYER_ID].LINE_WIDTH),
							dottedLine: f.STATUS != 0,
							zIndex: parseFloat(this.lineTypes[f.LAYER_ID].Z_INDEX ?? 10),
							data: f
						}
						let idx = this.getIdxOfFeatures(feature)
						if (idx == -1) {
							this.polyline.push(feature)
						} else if (cover) {
							this.polyline.splice(idx, 1, feature)
						}
					}
					features.push(feature)
				}
				return features
			},
			closePage() {
				if (this.gpsInterval) {
					clearInterval(this.gpsInterval);
					this.gpsInterval = null;
				}
				getApp().globalData.gpsNeed = 0
			}
		},
		onLoad(e) {
			this.currentIconUrl = uni.getStorageSync('currServerUrl') + '/ext/mapPath/icons/'
			this.url = e.dataURL
			this.mapContext = uni.createMapContext('map', this);
			this.rid = e.master.split('-')[1]
			if (this.url.indexOf("?") == -1) {
				this.url += "?"
			}
			if (e.master) {
				this.url += "master=" + e.master
			}
			this.xAjax({
				url: this.url + '&file=mapPath',
				hideLoading: true
			}, (res) => {
				let data = {}
				if (res.body) {
					data = res.body['rows'][0]
				}
				this.lineTypes = data.layers.LineString
				this.pointTypes = data.layers.Point
				if (data.center) {
					this.longitude = data.center[0]
					this.latitude = data.center[1]
				}
				if (data.zoom) {
					this.scale = data.zoom
				}
				this.layers = data.layers
			})
			getApp().globalData.gpsNeed = 1 //打开app.vue中的GPS定位开关
			this.gpsInterval = setInterval(() => {
				if (getApp().globalData.latitude) {
					if (!this.latitude) {
						this.longitude = getApp().globalData.longitude
						this.latitude = getApp().globalData.latitude
					}
					for (let i = 0; i < this.markers.length; i++) {
						if (this.markers[i].id == 0) {
							this.markers.splice(i, 1)
						}
					}
				}
			}, 2000)
		},
		onUnload() { //微信小程序中有效
			this.closePage()
		},
		onBackPress(e) { //APP中使用，微信小程序中无效
			this.closePage()
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		position: relative;
		height: 100vh;
		width: 100%;
	}

	.map {
		width: 100%;
		height: 100%;
	}

	/* 修改原有按钮样式定位 */
	.control-btn.top-right {
		right: 40rpx;
		top: 40rpx;
	}

	.control-btn {
		position: fixed;
		z-index: 999;
		right: 20px;
		top: 20px;
		/* 根据导航栏高度调整 */
		background: #fff;
		padding: 8px;
		border-radius: 50%;
		box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
	}

	.button-group {
		position: fixed;
		right: 20rpx;
		bottom: calc(40rpx + env(safe-area-inset-bottom));
		/* 适配安全区域 */
		z-index: 999;
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	/* 操作按钮样式 */
	.action-btn {
		width: 96rpx;
		height: 96rpx;
		background: #ffffff;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
		transition: all 0.2s;
	}

	/* 按钮点击效果 */
	.action-btn:active {
		transform: scale(0.92);
		opacity: 0.9;
	}

	.panel {
		position: fixed;
		top: 70px;
		/* 根据按钮位置调整 */
		right: 20px;
		z-index: 1000;
		width: 65%;
		max-width: 300px;
	}

	.panel-content {
		background: #fff;
		border-radius: 8px;
		padding: 10px;
		display: flex;
		flex-direction: column;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	}

	.panel-detail {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		grid-gap: 16rpx;
	}

	.btn-item {
		flex: 0 0 33.33%;
		display: flex;
		justify-content: center;
		min-height: 100rpx;
		border-radius: 10rpx;
		flex-direction: column;
		align-items: center;
		padding: 8px 5px;
	}

	.icon {
		width: 30px;
		height: 30px;
		margin-bottom: 5px;
	}

	.text {
		font-size: 20rpx;
		color: #666;
	}


	/* 三按钮容器 */
	.pin-button-container {
		position: fixed;
		bottom: 30rpx;
		left: 50%;
		transform: translateX(-50%);
		display: flex;
		gap: 50rpx;
		/* 按钮间距 */
		padding: 0 20rpx;
		z-index: 999;
		/* 安全区域适配 */
		padding-bottom: env(safe-area-inset-bottom);
	}

	/* 通用按钮样式 */
	.pin-button {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 120rpx;
		padding: 10rpx 10rpx;
		border-radius: 15rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
		transition: all 0.2s;

		&:active {
			opacity: 0.8;
			transform: scale(0.98);
		}
	}

	/* 打点按钮样式 */
	.mark-button {
		background: #007AFF;
	}

	/* 回退按钮样式 */
	.cancel-button {
		background: #E6A23C;
	}

	/* 完成按钮样式 */
	.confirm-button {
		background: #09BE4F;
	}

	.pin-button-text {
		color: #fff;
		font-size: 26rpx;
	}

	.detail-button {
		background: #09BE4F;
	}

	.delete-button {
		background: #FF0000;
	}

	//输入框输入后弹出的选项展示框
	.dropdown {
		position: absolute;
		top: 130rpx;
		width:90%;
		max-height: 400rpx;
		background-color: #fff;
		border: 1rpx solid #ccc;
		border-radius: 8rpx;
		z-index: 999;
	}
	
	.dropdown-item {
		padding: 20rpx;
		border-bottom: 1rpx solid #eee;
	}
	
	.dropdown-item:last-child {
		border-bottom: none;
	}
</style>