<template>
	<view>
		<view style="position: sticky;top:0;z-index:99;background-color: #f2f1f5;">
			<!--下一环节提示栏，可选环节-->
			<view class="uni-title" style="padding:10rpx;background-color: #fff;font-weight: 800;">
				{{btnLabel}}到
				<label class="radio" :class="{'chechedLabel':item.id==nextNodeId}" @tap="switchNode(item.id)" v-for="(item, index) in optionNodes" :key="index">
					<radio style="margin-left:10rpx;margin-right:-15rpx;transform:scale(0.7)" :value="item.id" @click="switchNode(item.id)" :checked="item.id==nextNodeId" />
					{{item.value}}
				</label>
			</view>
			<!-- 搜索栏 -->
			<view style="padding:10rpx; width:100%;background-color:#fff; display: flex;align-items:center;flex-direction: row;">
				<uni-icons type="search" style="position:absolute;left:20rpx;" size="25" color="#999"></uni-icons>
				<input type="text" style="background-color:#f2f1f5; padding:0 50rpx;width:100%;height:60rpx;border-radius: 15rpx;" @input="handlerFilter" v-model="keyword" class="option-filter" placeholder="输入关键字筛选批阅人">
				<uni-icons v-if="keyword" type="clear" style="position:absolute;right:20rpx;z-index:100;" size="25" color="#999" @click="clearSearchInput"></uni-icons>
			</view>
		</view>
		<!-- 待办人 -->
		<view style="margin-bottom:300rpx;">
			<view v-for="(item,index) in flowData.nodeHandlers" :key="index" class="list-cell">
				<view class="uni-title" style="display: flex;font-weight:800;background:linear-gradient(to bottom, #bedeff, #fff);height:70rpx;align-items: center">
					【{{item[0][1]}}】环节人员
				</view>
				<label v-for="(user,idx) in item" :key="idx" v-if="idx>0&&user[0].indexOf(keyword)>-1" :style="user[2]==true?'color:#3688ff':''" @click="checkUser(item[0][0],user[1])" class="uni-list-cell">
					<view>{{user[0]}}</view>
					<view><checkbox :checked="user[2]==true" :value="user[1]" /></view>
				</label>
			</view>
		</view>
		<!-- 确认提交按钮 -->
		<view class="btnContainer">
			<!-- 批阅栏 -->
			<textarea placeholder="输入批阅意见" v-model="opinion" style="margin:20rpx;padding:20rpx;width:710rpx;height:150rpx;background-color: #fff;border-radius: 25rpx;"/>
			<view style="display: flex;flex-direction: row;align-items: center;padding-bottom:20rpx;">
				<checkbox-group @change="checkMsg" >
					<view style="padding-left: 10rpx; display: flex; flex-direction: row; justify-content: flex-start;">
						<view v-if="flowData.sms!='undefine'&&flowData.noJob!=1" style="width:120rpx">
							短信<checkbox value='sms' :checked="flowData.sms==1" />
						</view>
						<view v-if="flowData.notify!='undefine'" style="width:150rpx">
							拟稿人<checkbox value='notify' :checked="flowData.notify==1" />
						</view>
					</view>
				</checkbox-group>
				<button @click="submitFlow()" class="submitBtn" type="default" style="background-color: #3688ff;color: #FFF;margin:0 20rpx;">提交流程</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		components: {
		},
		data: function() {
			return {
				searchVal:"",
				keyword:"",
				id: '',
				rid:'',
				taskid:'',
				isTask:'',
				master:'',
				thid:'',
				btnId:'',
				btnLabel:'',
				bidTimeout:'',
				nextNodeId: '',
				nextNodeName:'',
				initFlowData:[],//初始化值
				cacheFlowData:[],//缓存的初始值及各个可选节点的值
				flowData:[],//当前选中节点的值
				optionNodes:[],
				msg: [],
				opinion: ''
			}
		},
		onLoad(e) {
			uni.setNavigationBarTitle({
				title: e.title
			})
			this.id=e.id
			this.rid=e.rid
			this.thid=e.thid
			this.taskid=e.taskid
			this.isTask=e.isTask
			this.btnId=e.btnid
			this.btnLabel=e.btnlabel
			this.fromCache = e.fromCache==='true'
			this.optionNodes=getApp().globalData.flowData.nodeOptions
			this.initFlow(getApp().globalData.flowData)
		},
		methods: {
			handlerFilter:function(e){
				this.initFlow(this.flowData)
			},
			clearSearchInput:function(){
				this.keyword=''
			},
			initFlow: function(flowData){
				if(!this.cacheFlowData[flowData.defaultNode]){//没有缓存就处理
					for(var i=0;i< flowData.nodeOptions.length;i++){
						if(flowData.defaultNode==flowData.nodeOptions[i].id){
							this.nextNodeId  =flowData.nodeOptions[i].id
							this.nextNodeName=flowData.nodeOptions[i].value
						}
					}
					for(var i=0;i< flowData.nodeHandlers.length;i++){//第一层循环是可能的多个并行环节
						for(var k=1;k < flowData.nodeHandlers[i].length; k++){//第二层循环是节点定义及代办人
							var handler=flowData.nodeHandlers[i][k].split('^')
							var handlers=handler[handler.length-2];
							handler.splice(0,1);
							handler.splice(handler.length-2,1);
							var checked=false
							if((","+flowData.nodeHandlers[i][0][2]+",").indexOf(","+handlers+",")>-1||flowData.nodeHandlers[i].length==2||flowData.nodeHandlers[i][0][3]==5){//等于默认人、只有一个人、指派方式默选的默认选中
								checked=true
							}
							flowData.nodeHandlers[i][k]=[handler.join('-'),handlers,checked]
						}
					}
					this.cacheFlowData[flowData.defaultNode]=flowData
					this.flowData=flowData
				}
				this.$set(this,'flowData',this.cacheFlowData[flowData.defaultNode])
			},
			switchNode: function(nodeId){
				let that=this
				this.nextNodeId=nodeId
				if(!this.cacheFlowData[nodeId]){//无缓存就要去获取
					let url='wxChangeNode.php?id='+this.id+'&rid='+this.rid+'&bid='+this.btnId.substr(0,4)+nodeId
					this.xAjax({
					    url:url
					},function(res){
						that.initFlow(res)
					})
				}else{
					this.initFlow(this.cacheFlowData[nodeId])
				}
			},
			checkUser: function(nodeId,userId){
				for(var i=0;i< this.flowData.nodeHandlers.length;i++){//找到环节
					if(this.flowData.nodeHandlers[i][0][0]==nodeId){
						for(var k=1;k < this.flowData.nodeHandlers[i].length; k++){//第二层循环是节点定义及代办人
							if(this.flowData.nodeHandlers[i][k][1]==userId){
								this.$set(this.flowData.nodeHandlers[i][k],2,!this.flowData.nodeHandlers[i][k][2])
							}
						}
					}
				}
			},
			checkMsg: function(e){
				this.msg=e.detail.value
				if(this.msg.indexOf('sms')>-1){
					this.flowData.sms=1
				}else{
					this.flowData.sms=0
				}
				if(this.msg.indexOf('notify')!=-1){
					this.flowData.notify=1
				}else{
					this.flowData.notify=0
				}
			},
			submitFlow: function(){
				if (this.bidTimeout) { //避免按钮连续点击，3秒后才能重新点击
					return
				} else {
					this.bidTimeout = setTimeout(function() {
						clearTimeout(that.bidTimeout)
						that.bidTimeout = null
					}, 2000)
				}
				var that=this
				var err=[];
				var allhandler='';
				for(var i=0;i< this.flowData.nodeHandlers.length;i++){
					var handler='';
					for(var j=1;j< this.flowData.nodeHandlers[i].length;j++){
						if(this.flowData.nodeHandlers[i][j][2]==true||this.flowData.nodeHandlers[i][0][3]=='3'){
							handler+=','+this.flowData.nodeHandlers[i][j][1];
						}
					}
					if(handler==''){
						err.push(this.flowData.nodeHandlers[i][0][1]);
					}else{
						allhandler+='^'+this.flowData.nodeHandlers[i][0][0]+'`'+handler.substr(1) + '`' + this.flowData.nodeHandlers[i][0][5];
					}
				}
				if(err.length>0&&this.flowData.noJob!='1'){
					uni.showModal({
						title: '错误提示',
						content:'请选择'+err.join(',')+'环节的处理人员'
					})
				}else{
					var data={
						app  : '4',
						id   : this.id,
						rid  : this.rid,
						thid : this.thid,
						taskid:this.taskid,
						btnlabel:this.btnLabel,
						bid  :this.btnId,
						targetid:this.nextNodeId,
						handler:allhandler.substr(1),
						sms  : this.flowData.sms||0,
						notify: this.flowData.notify||0,
						opinion:this.opinion
					}
					let url='wxFlow.php'
					this.xAjax({
					    url:url, 
					    data:data
					},function(res){
						if(that.isTask==1){//如果是待办单过来的要关闭edit窗口并刷新待办页面
							uni.$emit('refreshList')
							uni.navigateBack({delta:2})
						}else{
							uni.$emit('initEdit',{id:that.id,rid:that.rid})
							uni.$emit('refreshList')
							uni.navigateBack()
						}
						if(that.fromCache){
							var cacheLists = uni.getStorageSync('cacheLists')
							delete cacheLists[getApp().globalData.userId][that.id][that.rid]
							uni.setStorageSync('cacheLists',cacheLists)
							uni.$emit('reloadListCache')
							uni.navigateBack()
						}
					})
				}
			}
		}
	}
</script>

<style>

	page {
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
		background-color: #f2f1f5
	}

	view {
		line-height: inherit
	}
	
	checkbox {
		transform:scale(0.8)
	}
	
	.list-cell{
		position: relative;
		width: 100%;
		margin-bottom: 20rpx;
		background-color: #fff;
	}
	
	.chechedLabel {
		color:#007AFF;
	}

	.btnContainer {
		background-color: #f2f1f5;
		width:100%;
		z-index:99;
		position: fixed;
		bottom:0;
	}

	.submitBtn {
		height: 70rpx;
		font-size: 28rpx;
		border-radius: 10rpx;
		width:100%;
	}

	.uni-list-cell {
		padding: 10rpx;
		display: flex;
		align-items:center;
		justify-content: space-between;
	}
	
</style>
