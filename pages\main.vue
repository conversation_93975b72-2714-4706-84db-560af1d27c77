<template>
	<view>
		<zxnav :topTitle="topTitle"></zxnav>
		<view class="top_menu" :style="[{'top':statusHeight+'px'}]">
			<view v-for="(item,index) in topMenu" :key="index" @click="goList(item, 'all')">
				<uni-badge class="uni-badge-left-margin" :text="item.badge" absolute="rightTop" :offset="[1, 1]" size="small">
					<view class="icon9__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-color-white" :style="'background-color:'+item.bgcolor"><!--背景色1 :style="'background-color:'+item.color" 背景色2 :class="item.bgcolor" -->
						<view :class="[`tn-icon-${item.icon}`]"></view>
					</view>
				</uni-badge>
				<text class="tn-text-sm tn-flex tn-flex-row-center">{{item.label}}</text><!--必须保留 tn-flex tn-flex-row-center，否则不居中-->
			</view>
		</view>
		<!-- </view> -->
		<view class="noticeBar" :style="[{'top':'calc(187rpx + '+statusHeight+'px)'}]">
			<!--滚动通告栏-->
			<uni-notice-bar 
				color="#2979FF"
				background-color="#EAF2FF"
				show-icon 
				show-get-more
				moreColor="#2979FF"
				@click="getMoreNotice"
				@getmore="getMoreNotice"
				:text="noticeTitle"
			/>
			<!-- 分类导航栏，常用应用/全部应用 -->
			<view style="position: relative;">
				<view style="width:370rpx">
					<uni-segmented-control
						style-type="text"
						active-color="#007aff" 
						:current="currentTab" 
						:values="items" 
						@clickItem="onClickTab" 
					/>
					<view style="width:100%;position:absolute;border-top: solid 1rpx #ddd;top:65rpx;z-index: -1;"></view>
				</view>
				<view @click="setMyMenu" style="position:absolute;top:10rpx;right:40rpx;display: flex;align-items: center;">
					<uni-icons type="plus" color="#777" size="22" />设置常用
				</view>
			</view>
			<!-- 常用应用与全部应用tab标签页 -->
			<view v-if="currentTab === 0">
				<!-- 常用应用 -->
				<view class="tn-flex tn-flex-wrap">
					<view v-for="(item,index) in myMenu" :key="index" @click="goList(item,'myMenu')">
						<view style="width:187.5rpx;height:160rpx;">
							<view :class="item.bgcolor?'':item.bgclass" :style="'background-color:'+item.bgcolor" class="icon icon__item--icon4 tn-flex tn-flex-row-center tn-flex-col-center tn-color-white">
								<view v-if="item.icon.indexOf('tn-icon')>-1" :class="item.icon" style="transform: rotate(19deg);"></view>
								<mdi-icon v-else :icon="item.icon" color="white" size="35" class="mdi" rotate="19"/>
								<view style="position: absolute;top:-30rpx;right:-30rpx;transform: rotate(19deg);z-index:15;">
									<uni-icons v-if="setState&&item.setState" type="minus" color="red" size="25" />
								</view>
							</view>  
							<text class="tn-text-sm tn-flex tn-flex-row-center">{{item.label}}</text>
						</view>
					</view>
				</view>
				
				<view class="grid-title">
					<text>最近使用</text>
					<view @click="clearMyRecent" style="position:absolute;right:40rpx;display: flex;align-items: center;font-weight: 500;">
						<uni-icons type="close" color="#777" size="22" />清除
					</view>
				</view>
				<view class="tn-flex tn-flex-wrap">
					<view v-for="(item,index) in myRecent" :key="index">
						<view style="width:187.5rpx;height:160rpx;">
							<view @click="goList(item,'myRecent')" @longpress="deleteMyrecent(item)" :class="item.bgcolor?'':item.bgclass" :style="'background-color:'+item.bgcolor" class="icon icon__item--icon4 tn-flex tn-flex-row-center tn-flex-col-center tn-color-white">
								<view v-if="item.icon.indexOf('tn-icon')>-1" :class="item.icon" style="transform: rotate(19deg);"></view>
								<mdi-icon v-else :icon="item.icon" color="white" size="35" class="mdi" rotate="19"/>
								<view v-if="setState&&item.setState" style="position: absolute;top:-30rpx;right:-30rpx;transform: rotate(19deg);z-index:15;">
									<uni-icons type="plus" color="red" size="25" />
								</view>
							</view>  
							<text class="tn-text-sm tn-flex tn-flex-row-center">{{item.label}}</text>
						</view>
					</view>
				</view>
				
			</view>
			<!-- 全部应用 -->
			<view v-if="currentTab === 1" v-for="(dir,idx) in allMenu">
				<view v-if="!dir.hide">
					<view class="grid-title">
						<text>{{dir.menu}}</text>
					</view>
					<view class="tn-flex tn-flex-wrap">
						<view v-for="(item,index) in dir.list" :key="index">
							<view style="width:187.5rpx;height:160rpx;" v-if="!item[4]">
								<view @tap="goList(item, 'all')" :class="item.bgcolor?'':item.bgclass" :style="'background-color:'+item.bgcolor" class="icon icon__item--icon4 tn-flex tn-flex-row-center tn-flex-col-center tn-color-white">
									<view v-if="item.icon.indexOf('tn-icon')>-1" :class="item.icon" style="transform: rotate(19deg);"></view>
									<mdi-icon v-else :icon="item.icon" color="white" size="38" class="mdi" rotate="19"/>
									<view v-if="setState&&item.setState" style="position: absolute;top:-30rpx;right:-30rpx;transform: rotate(19deg);">
										<uni-icons type="plus" color="red" size="25" />
									</view>
								</view>  
								<text class="tn-text-sm tn-flex tn-flex-row-center">{{item.label}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {soterAuth} from '../static/js/soterAuth.js'
	import zxnav from '../components/zxnav/zxnav.vue'
	import mdiIcon from 'pagesA/components/mdi-icon/components/mdi-icon/mdi-icon.vue'
	export default {
		components: {
			zxnav,
			mdiIcon
		},
		data(){
			return {
				currentTab: 0,
				items: ['常用应用', '全部应用'],
				allMenu: [],
				serverName: '',
				setState: false,
				topTitle: '在线',
				authDeny: false,
				topMenu: [
					{isTop:1, id:"mytask", label:"待办", icon:"edit-write-fill", bgcolor:"#4B98FE"},
					{isTop:1, id:"myunread", label:"待阅", icon:"eye-fill", bgcolor:"#00B9FE"},
					{isTop:1, id:"myMessage", label:"消息", icon:"message-fill", bgcolor:"#00D05E"},
					{isTop:1, id:"myitem", label:"发起", icon:"write-fill", bgcolor:"#FFAC00"},
					{isTop:1, id:"mytrace", label:"关注", icon:"star-fill", bgcolor:"#FE871B"}
					// #4B98FE #FFAC00 #00D05E #FE871B #00C8B0 #00B9FE #FB6A67 #957BFE
				],
				noticeTitle: '暂无未读公告',
				myMenu:[],
				myRecent:[],
				color:[
					'red',
					'purplered',
					'purple',
					'bluepurple',
					'aquablue',
					'blue',
					'indigo',
					'cyan',
					'teal',
					'green',
					'yellowgreen',
					'lime',
					'yellow',
					'orangeyellow',
					'orange',
					'orangered',
					'brown'
				],
				statusHeight: 72
			}
		},
		methods: {
			getRandomInteger(min, max) {
				return Math.floor(Math.random() * (max - min + 1)) + min;
			},
			getMoreNotice(){
				uni.navigateTo({
					url: "/pages/list?id=bulletin"
				})
			},
			getQuery() {
				this.tabQuery = this.list.map(item => {
					return item.menu;
				})
			},
			dealScanData(res){
				if (res.indexOf("{") > -1) { //表明是json
					this.scanParse(res)
					return
				}
				uni.setClipboardData({
					data: res,
					success: function() {
						uni.showModal({
							title: "非本系统格式，已复制到粘贴板:" + res
						})
					}
				})
			},
			showApp(menu, index) {
				this.selectedInx = index;
				for (let item of this.list) {
					if (item.menu == menu) {
						this.currentList = item.list;
						break;
					}
				}
			},
			onClickTab(e){
				this.currentTab=e.currentIndex
			},
			clearMyRecent(){
				this.myRecent=[]
				uni.setStorageSync(this.serverName +'RecentMenu',[])
			},
			deleteMyrecent(e){
				for(var i=0; i< this.myRecent.length; i++){
					if(this.myRecent[i].id==e.id){
						this.myRecent.splice(i,1);
					}
				}
				uni.setStorageSync(this.serverName +'RecentMenu',this.myRecent)
			},
			setMyMenu(){
				this.setState=!this.setState
				let myMenu=[];
				for(var i in this.myMenu){
					myMenu.push(this.myMenu[i].id)
					this.$set(this.myMenu[i], "setState", true)
					this.myMenu[i].setState = true
				}
				for(var i in this.myRecent){
					this.$set(this.myRecent[i], "setState", myMenu.indexOf(this.myRecent[i].id)<0)
				}
				for(var i in this.allMenu){
					for(var k in this.allMenu[i].list){
						this.$set(this.allMenu[i].list[k], "setState", myMenu.indexOf(this.allMenu[i].list[k].id)<0)
					}
				}
			},
			getMenu() {
				let that = this
				let url = 'wxAllMenu.php'
				this.xAjax({
					url: url
				}, function(res) {
					that.allMenu = res.allMenu
					that.myMenu = res.myMenu
					for(var i in that.allMenu){
						for(var k in that.allMenu[i].list){
							that.$set(that.allMenu[i].list[k], "icon", that.allMenu[i].list[k].icon||'tn-icon-iot-fill')
							that.$set(that.allMenu[i].list[k], "bgclass", 'tn-bg-' + that.color[that.getRandomInteger(0,16)] + "--dark")
						}
					}
					for(var i in that.myMenu){
						if(that.myMenu[i].id){
							that.$set(that.myMenu[i], "icon", that.myMenu[i].icon||'tn-icon-iot-fill')
							that.$set(that.myMenu[i], "bgclass", 'tn-bg-' + that.color[that.getRandomInteger(0,16)] + "--dark")
						}
					}
				})
				this.getTaskInfo()
			},
			goList(e,src) {
				let that=this
				if(this.setState){//设置常用开启后
					if(e.isTop)return
					if(src=="myMenu"){//从常用中取消
						this.xAjax({
							url:"sysTaskShortcutDel.php?zID="+e.id
						},function(){
							for(var i in that.myMenu){
								if(that.myMenu[i].id==e.id){
									that.myMenu.splice(i,1)
									break
								}
							}
							for(var i in that.myRecent){
								if(that.myRecent[i].id==e.id){
									that.$set(that.myRecent[i], "setState", true)
									break
								}
							}
							for(var i in that.allMenu){
								for(var k in that.allMenu[i].list){
									if(that.allMenu[i].list[k].id==e.id){
										that.$set(that.allMenu[i].list[k], "setState", true)
										break
									}
								}
							}
						})
					}else{//添加到常用
						let match=false
						//添加动作
						this.xAjax({
							url:"sysTaskShortcutAdd.php?zID="+e.id
						},function(){
							for(var i in that.myMenu){
								if(that.myMenu[i].id==e.id){
									match=true
								}
							}
							if(!match){
								let item={id:e.id, label:e.label, bgclass:e.bgclass, bgcolor:e.bgcolor, icon:e.icon, setState:true};
								that.myMenu.push(item)
							}
							//从最近和全部应用中状态变更为不可选
							for(var i in that.myRecent){
								if(that.myRecent[i].id==e.id){
									that.$set(that.myRecent[i],"setState",false)
									break
								}
							}
							for(var i in that.allMenu){
								for(var k in that.allMenu[i].list){
									if(that.allMenu[i].list[k].id==e.id){
										that.$set(that.allMenu[i].list[k], "setState", false)
										break
									}
								}
							}
						})
					}
					return
				}
				if(!e.id)return
				
				let url = '/pages/list?id=' + e.id + '&label=' + e.label
				if (e.url) {
					if (e.url.indexOf("http") > -1) {
						url = '/pages/link?id=' + e.id + '&label=' + e.label + '&url=' + encodeURIComponent(e.url)
					} else {
						url = e.url
						if (url.indexOf("?") < 0) {
							url += "?"
						}
						if (url.indexOf("&id=") < 0 && url.indexOf("?id=") < 0) {
							url += "&id=" + e.id
						}
						if (url.indexOf("&label=") < 0 && url.indexOf("?label=") < 0) {
							url += "&label=" + e.label
						}
					}
				}
				uni.navigateTo({
					url: url
				})
				if(!e.isTop){//待办栏不添加到最近使用
					for(var i=0; i< this.myRecent.length; i++){
						if(this.myRecent[i].id==e.id){
							this.myRecent.splice(i,1);
						}
					}
					//这里不能直接添加e，会影响myMenu的设置状态的渲染
					this.myRecent.unshift({id:e.id, label:e.label, url:e.url, bgclass:e.bgclass, bgcolor:e.bgcolor, icon:e.icon});
					this.myRecent.splice(20);
					uni.setStorageSync(this.serverName +'RecentMenu',this.myRecent)
				}
			},
			getTaskInfo() {
				let that = this
				let url = 'wxTaskInfo.php'
				this.xAjax({
					url: url,
					hideAll: true,
					allwaysCall: true
				}, function(res) {
					if (res.err) {
						return
					}
					if (res.timeout) {
						clearInterval(that.timer)
						return
					}
					if (res.bulletin) {
						that.noticeTitle = res.bulletin
					}
					let taskTotal = 0;
					//待办总数
					let taskCount = ''
					if (res.taskCount > 0) {
						taskCount = res.taskCount.toString()
						taskTotal = res.taskCount
					}
					that.$set(that.topMenu[0], "badge", taskCount)
					//抄送总数
					let ccCount = ''
					if (res.ccCount > 0) {
						ccCount = res.ccCount.toString()
						taskTotal += res.ccCount
					}
					that.$set(that.topMenu[1], "badge", ccCount)
					//系统消息数量
					let msgCount = ''
					if (res.msgCount > 0) {
						msgCount = res.msgCount.toString()
						taskTotal += res.msgCount
					}
					that.$set(that.topMenu[2], "badge", msgCount)
					//导航栏显示总数量
					if (taskTotal > 0) {
						taskTotal = taskTotal.toString()
						uni.setTabBarBadge({
							index: 2,
							text: taskTotal
						})
					} else {
						uni.removeTabBarBadge({
							index: 2							
						})
					}
					
					// uni.setTabBarBadge({
					// 	index: 4,
					// 	text: msgCount
					// })
					// getApp().globalData.sysMessageCount = msgCount
					// uni.$emit('setMessageCount', msgCount)
				}) //此参数定义为后台执行，不显示加载状态
			}
		},
		onReady() {
			// #ifdef MP-WEIXIN
			this.statusHeight = uni.getMenuButtonBoundingClientRect().top+uni.getMenuButtonBoundingClientRect().height
			// #endif
			// #ifdef APP-PLUS
			this.statusHeight = 72
			if(!uni.getStorageSync('showSoterAuthTip')&&!uni.getStorageSync('isSoterAuth')){
				uni.showModal({
					title: '提示',
					content: '是否启用指纹登录',
					confirmText: '是',
					cancelText: '否',
					success(res) {
						if (res.confirm) {
							soterAuth('main',()=>{
								uni.setStorage({
									key: 'isSoterAuth',
									data: 1
								})
							})
						}
						if (res.cancel) {
							uni.showToast({
								title: '可在个人中心开启指纹登录',
								duration: 3000
							});
							uni.setStorageSync('showSoterAuthTip',1)
						}
					}
				})
			}
			// #endif
			// #ifdef H5
			this.statusHeight = 32
			// #endif
			let that=this
			if(getApp().globalData.offline){
				this.topTitle='离线'
			}
			uni.$on('setTopOnline',function(){
				that.topTitle='在线'
			})
			//H5认证后直接跳转到相对路径，无需用绝对IP路径
			// #ifdef H5
			uni.setStorageSync('currServerUrl','../')
			// #endif
			
			if(!getApp().globalData.offline){
				this.getMenu()
			}
			this.timer = setInterval(() => { //5分钟定时刷新待办和公告
				this.getTaskInfo()
			}, 300000)
			this.serverName=uni.getStorageSync('currServer').operator
			var myRecent=uni.getStorageSync(this.serverName+'RecentMenu')
			if(myRecent){
				this.myRecent=myRecent
			}
			uni.$on('searchMenu', function(e) {
				that.currentTab=1
				for(var i=0; i< that.allMenu.length; i++){
					that.allMenu[i].hide=false
					that.allMenu[i].menuNum=0
					for(var k=0; k< that.allMenu[i].list.length; k++){
						if(that.allMenu[i].list[k].label.indexOf(e.keyword)<0&&that.allMenu[i].menu.indexOf(e.keyword)<0){//不含搜索关键字的要隐藏掉
							that.$set(that.allMenu[i].list[k],"hide",true)
						}else{
							that.$set(that.allMenu[i].list[k],"hide",false)//含了关键字的要显示
							that.allMenu[i].menuNum+=1//菜单不隐藏
						}
					}
				}
				for(var i=0; i< that.allMenu.length; i++){
					if(that.allMenu[i].menuNum==0){
						that.allMenu[i].hide=true
					}
				}
			})
		},
		onPullDownRefresh() {
			this.setState=false
			this.getMenu()
		},
		onShareAppMessage(res) {
			return {
				path: '/pages/login'
			}
		},
		onShow() {
			this.getTaskInfo()
		}
	}
</script>

<style lang="scss" scoped>
	
	.image {
		width: 80rpx;
		height: 80rpx;
	}
	
	.top_menu {
		position: fixed;
		width:750rpx;
		padding:30rpx 40rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		z-index: 999;
		background-color: #fafbfc;
	}
	
	//图标的间隔
	.icon {
		margin:30rpx 0 10rpx 45rpx;
	}
	
	.mdi {
		display:flex;
		justify-content: center;
		align-items: center;
	}
	
	.icon9 {
		&__item {
			width: 30%;
			background-color: #FFFFFF;
			border-radius: 10rpx;
			transform: scale(1);
			transition: transform 0.3s linear;
			transform-origin: center center;
			
			&--icon {
				width: 75rpx;
				height: 75rpx;
				font-size: 50rpx;
				border-radius: 50%;
				margin-bottom: 18rpx;
				position: relative;
				z-index: 1;
			  
				&::after {
					content: " ";
					position: absolute;
					z-index: -1;
					width: 100%;
					height: 100%;
					left: 0;
					bottom: 0;
					border-radius: inherit;
					opacity: 1;
					transform: scale(1, 1);
					background-size: 100% 100%;
					//background-image: url(https://tnuiimage.tnkjapp.com/cool_bg_image/icon_bg6.png);
				}
			}
		}
	}
	
	.noticeBar {
		position: absolute;
		margin:0;
		padding-bottom:30rpx;
		width:750rpx;
		z-index: 1;
	}
	
	.item {
		flex: 1;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 5px 0;
		margin: 10rpx 0rpx;
		font-size: 25rpx;
		.text{
			margin-top: 5rpx;
		}
	}
	
	.grid-item-box {
		flex: 1;
		// position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 15px 0;
		font-size: 25rpx;
	}
	
	.grid-title {
		position: relative;
		display: flex;
		flex-direction: row;
		margin:20rpx 0 0 35rpx;
		font-weight: bold;
	}
	
	/* 以下是重要代码 */
	/* 以下是重要代码 */
	/* 以下是重要代码 */
	/* 以下是重要代码 */
	/* 以下是重要代码 */
	/* 以下是重要代码 */
	
	/* 温馨提示：为什么那么多重复的代码？因为方便用户微调对应的样式，且不相互影响，或者只拿对用的图标样式代码也可以直接使用*/
	
	.icon {
		&__item {
	    
	    /* 为了布局的而已*/
			// background-color: #FFFFFF;
			padding: 30rpx;
			margin: 20rpx 10rpx;
			transform: scale(1);
			transition: transform 0.2s linear;
			transform-origin: center center;
	    
	    /* 字体形式*/
			/* 正圆*/
			&--icon3 {
				width: 100rpx;
				height: 100rpx;
				font-size: 60rpx;
				border-radius: 50%;
				margin-bottom: 18rpx;
				position: relative;
				z-index: 1;
			  
				&::after {
					content: " ";
					position: absolute;
					z-index: -1;
					width: 100%;
					height: 100%;
					left: 0;
					bottom: 0;
					border-radius: inherit;
					opacity: 1;
					transform: scale(1, 1);
					background-size: 100% 100%;
					//background-image: url('/static/iconbg/icon_bg.png');
				}
			}
			
			/* 超椭圆*/
			&--icon4 {
				width: 100rpx;
				height: 100rpx;
				font-size: 60rpx;
				transform: rotate(-19deg);
				border-radius: 43% 57% 43% 57% / 57% 43% 57% 43%;
				margin-bottom: 18rpx;
				position: relative;
				z-index: 1;
			  
				&::after {
					content: " ";
					position: absolute;
					z-index: -1;
					width: 100%;
					height: 100%;
					left: 0;
					bottom: 0;
					border-radius: inherit;
					opacity: 1;
					transform: scale(1, 1);
					background-size: 100% 100%;
					//background-image: url('/static/iconbg/icon_bg5.png');
				}
			}
			
			
			/* 圆角*/
			&--icon5 {
				width: 100rpx;
				height: 100rpx;
				font-size: 60rpx;
				border-radius: 25%;
				margin-bottom: 18rpx;
				position: relative;
				z-index: 1;
			  
				&::after {
					content: " ";
					position: absolute;
					z-index: -1;
					width: 100%;
					height: 100%;
					left: 0;
					bottom: 0;
					border-radius: inherit;
					opacity: 1;
					transform: scale(1, 1);
					background-size: 100% 100%;
					//background-image: url('/static/iconbg/icon_bg6.png');
				}
			}
			
		}
	}
	
	
</style>
