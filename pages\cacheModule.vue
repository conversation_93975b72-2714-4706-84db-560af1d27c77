<template>
	<view class="content">
		<uni-segmented-control
		style="width: 100%;background: #fff;"
			:current="current" 
			:values="['离线列表','离线图片']" 
			style-type="text"
			active-color="#007aff"
			@clickItem="onClickSegmented" 
		/>
		<template v-if="current==0">
			<view v-if="!hasData" style="width: 100%;color: grey;padding: 25rpx;">
				<image src="@/static/img/example.jpg" style="width: 700rpx;height: 375rpx;box-shadow:0px 0px 10px #888;border-radius:10rpx" alt="" />
				<text>如果列表右上角有<text class="tn-icon-star"></text>可点击进行离线缓存此表单</text>
			</view>
			<view v-if="!hasData" style="display:flex;width:100%;height:35vh;justify-content:center;align-items: center;">
				<tn-empty mode="data">
				</tn-empty>
			</view>
			<block v-for="(key,index) in Object.keys(cacheListData)" :key="key">
				<view v-if="Object.keys(cacheListData[key]).length>1" class="item" @click="goCacheList(key)">
					<text style="font-size: 32rpx;font-weight: 600;">{{cacheListData[key].label}}</text>
					<text style="font-size: 24rpx;color: grey;margin-top: 10rpx;">离线表单数:{{Object.keys(cacheListData[key]).length-1}}</text>
				</view>
			</block>
		</template>
		<view v-if="current==1" style="width: 100%;">
			<cachePicVue ref="cachePic"/>
		</view>
	</view>
</template>

<script>
	import cachePicVue from './cachePic.vue'
	export default {
		data() {
			return {
				cacheListData:{},
				hasData:false,
				current:0
			}
		},
		components:{cachePicVue},
		methods: {
			getData(){
				this.hasData = false
				let userId = uni.getStorageSync('userId')
				let cacheLists = uni.getStorageSync('cacheLists')
				if(!getApp().globalData.offline){
					userId = getApp().globalData.userId
				}
				if(cacheLists&&cacheLists[userId]){
					this.cacheListData = cacheLists[userId]
				}
				for(let item of Object.keys(this.cacheListData)){
					if(Object.keys(this.cacheListData[item]).length>1){
						this.hasData = true
					}
				}
			},
			onClickSegmented(e){
				this.current=e.currentIndex
			},
			goCacheList(key){
				uni.navigateTo({
					url:'/pages/cacheList?id='+key
				})
			}
		},
		onLoad() {
			this.getData()
		},
		onShow(){
			if(this.current==0){
				this.getData()
			}
			if(this.current==1){
				this.$refs['cachePic'].loadData()
			}
		},
		onPullDownRefresh(){
			if(this.current==0){
				this.getData()
			}
			if(this.current==1){
				this.$refs['cachePic'].loadData()
			}
			uni.stopPullDownRefresh()
		}
	}
</script>
<style>
	page{
		background-color: #f6f5f9;
	}
</style>
<style scoped>
.content{
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.item{
	position: relative;
	width: 710rpx;
	margin-top: 20rpx;
	padding: 25rpx;
	border-radius: 20rpx;
	background-color: #fff;
	display: flex;
	flex-direction: column;
}
</style>
