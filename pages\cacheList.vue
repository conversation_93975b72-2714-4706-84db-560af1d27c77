<template>
	<view class="main">
		<view v-if="!hasData" style="display:flex;width:100%;height:80vh;justify-content:center;align-items: center;">
			<tn-empty mode="data"></tn-empty>
		</view>
		<view v-else class="menu-item" v-for="(zid,index) in Object.keys(cacheLists[userId])" :key="zid">
			<block v-if="Object.keys(cacheLists[userId][zid]).length>0&&(id&&zid==id||!id)">
				<view class="menu-label" v-if="!id">{{cacheLists[userId][zid].label}}</view>
				<view class="list-cell" v-for="rid,idx in Object.keys(cacheLists[userId][zid])" v-if="rid!='label'" @click="goDetail(zid, rid)" :key="rid">
					<view v-for="(col,colIdx) in cacheLists[userId][zid][rid].listData" :key="colIdx">
						<text style="margin-right: 20rpx;">{{col.title+':'}}</text>
						<text>{{ col.value||"" }}</text>
					</view>
					<view style="position:absolute; right:10rpx; top:20rpx; width:40rpx; height:40rpx" @tap.stop="refreshEdit(zid,rid)"><!-- 注意：点击方法不能放在icons中，不知道原因，list中可以放？？？ -->
						<uni-icons type="reload" color="#888" size="40rpx"></uni-icons>
					</view>
					<view style="position:absolute; right:10rpx; bottom:20rpx; width:40rpx; height:40rpx" @click.stop="deleteCache(zid,rid)">
						<uni-icons type="star-filled" color="orange" size="40rpx"></uni-icons>
					</view>
				</view>
			</block>	
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				cacheLists: {},
				hasData: false,
				userId:'',
				id:''
			}
		},
		components:{

		},
		methods: {
			goDetail(zid, rid){
				let url = 'edit?id=' + zid + '&rid=' + rid + '&fromCache=1'
				uni.navigateTo({
					url:url
				})
			},
			deleteCache(zid, rid){
				let that = this
				uni.showModal({
					title:'提示',
					content:'确认要取消离线缓存吗',
					success(c) {
						if(c.confirm){
							that.$delete(that.cacheLists[that.userId][zid],rid)
							if(Object.keys(that.cacheLists[that.userId][zid]).length<2){//只剩下了label,没有记录的要删除模块
								that.$delete(that.cacheLists[that.userId], zid)
								if(Object.keys(that.cacheLists[that.userId]).length==0){
									that.hasData = false
								}
							}
							uni.setStorageSync('cacheLists',that.cacheLists)
						}
					}
				})
			},
			refreshEdit(zid, rid){
				var that = this
				let url = this.cacheLists[this.userId][zid][rid]['editUrl']
				this.xAjax({
					url: url
				}, function(res) {
					that.cacheLists[that.userId][zid][rid]['formData'] = res
					delete that.cacheLists[that.userId][zid][rid]['updateData']
					let tabList = []
					for(let item of res.datas){
						if(item.type=='ZC'||item.type=='FORMTAB'){
							tabList.push(item)
						}
					}
					if(tabList.length>0){
						that.loadTabData(tabList,0,{},(tabData)=>{
							that.cacheLists[that.userId][zid][rid]['tabData']=tabData
							uni.setStorageSync('cacheLists',that.cacheLists)
						})
					}else{
						uni.setStorageSync('cacheLists',that.cacheLists)
					}
				});
			},
			loadTabData(tabList,idx,tabData,callback){
				let item = tabList[idx]
				let url = 'wxList.php?id=' + item.id
				if (item.master) {
					url += '&master=' + item.master
				}
				if (item.popget) {//弹出列表需要获取带回的字段值
					url += '&userdata=' + item.popget
				}
				this.xAjax({
					url: url
				}, (res) =>{
					// tabData[item.name] = res
					// if(tabList.length==++idx){
					// 	callback(tabData)
					// }else{
					// 	this.loadTabData(tabList,idx,tabData,callback)
					// }
					let promises = [];
						if(!res.enableForm){
						tabData[item.name] = res
						if(tabList.length==++idx){
							callback(tabData)
						}else{
							this.loadTabData(tabList,idx,tabData,callback)
						}
						return
						}
						for(let row of res.data.rows){
							let p = new Promise((res)=>{
								let url = this.editURL?this.editURL:'wxEdit.php' + '?id=' + item.id + '&rid=' + row.id + '&cache=1'
								if (this.master) {
									url += '&master=' + this.master
								}
								this.xAjax({
									url: url,
									hideLoading:false
								}, (resData) =>{
									row.form = resData
									res()
								})
							})
							promises.push(p)
						}
						Promise.all(promises).then(()=>{
							tabData[item.name] = res
							if(tabList.length==++idx){
								callback(tabData)
							}else{
								this.loadTabData(tabList,idx,tabData,callback)
							}
							uni.showToast({
								title:'刷新成功'
							})
						})
					})
			},
			reloadListCache(){
				this.userId = getApp().globalData.userId
				this.cacheLists = uni.getStorageSync('cacheLists')
				if(this.cacheLists[this.userId]){
					if(Object.keys(this.cacheLists[this.userId]).length>0){
						this.hasData = true
					}
				}
			}
		},
		onLoad(e) {
			uni.$on('reloadListCache',()=>{
				this.reloadListCache()
				if(e.id){
					this.id = e.id
					uni.setNavigationBarTitle({
						title:'离线列表('+this.cacheLists[this.userId][e.id].label+')'
					})
				}
			})
			this.reloadListCache()
			if(e.id){
				this.id = e.id
				uni.setNavigationBarTitle({
					title:'离线列表('+this.cacheLists[this.userId][e.id].label+')'
				})
			}
			/*缓存的数据格式
			{
			    "21232f297a57a5a743894a0e4a801fc3": {//用户id
			        "demo": {//模块id
			            "label": "演示页面",
			            "colTitle": [
			                "单行编辑",
			                "创建人",
			                "当前环节",
			                "创建时间"
			            ],
			            "638c40c89641d": {//记录主键值
			                "listData": [
			                    "2",
			                    "故障原因",
			                    "",
			                    "结束",
			                    "2022-12-04 14:40:20"
			                ],
			                "formData": {
			                    "code": 0,
			                    "developid": "60f51917c7600",
			                    "layout": "right",
			                    "pkey": "RID",
			                    "realid": "demo",
			                    "editUpdateURL": "",
			                    "realrid": "638c40c89641d",
			                    "appdisable": "0",
			                    "istask": "",
			                    "taskid": "",
			                    "thid": 998
			                },
			                "editUrl": "wxEdit.php?id=demo&rid=638c40c89641d"
			            }
			        },
			        "RequestHN": {//模块id
			            "label": "光网需求",
			            "colTitle": [
			                "需求名称",
			                "区县公司",
			                "当前环节",
			                "建设属性",
			                "工程类型",
			                "创建人",
			                "创建日期"
			            ],
			            "645c80b08f3a2": {//记录主键值rid值
			                "listData": [
			                    "1",
			                    "测试20230511",
			                    "襄城区电信局",
			                    "草稿",
			                    "新建",
			                    "自建(FTTH)",
			                    "系统管理员",
			                    "2023-05-11 13:45:40"
			                ],
			                "formData": {
			                    "code": 0,
			                    "developid": "100003458611981",
			                    "layout": "",
			                    "pkey": "RID",
			                    "realid": "RequestHN",
			                    "editUpdateURL": "",
			                    "realrid": "645c80b08f3a2",
			                    "appdisable": "0",
			                    "istask": "",
			                    "taskid": "",
			                    "thid": 1,
			                    "receiveuser": "",
			                    "title": "拟稿"
			                },
			                "editUrl": "wxEdit.php?id=RequestHN&rid=645c80b08f3a2"
			            }
			        }
			    }
			}
			*/
		}
	}
</script>
<style>
	page {
		background-color: #f6f5f9;
	}
</style>
<style scoped>
	.main {
		display: flex;
		align-items: center;
		flex-direction: column;
		width: 100%;
		min-height: 100%;
		padding-bottom: 50rpx;
	}
	.menu-label{
		align-self: flex-start;
		margin-left: 5%;
		margin-top: 20rpx;
		font-weight: 600;
	}
	.menu-item {
		width: 100%;
		display: flex;
		align-items: center;
		flex-direction: column;
	}

	.list-cell{
		position: relative;
		width: 710rpx;
		margin-top: 20rpx;
		padding: 25rpx;
		border-radius: 20rpx;
		background-color: #fff;
	}
	
</style>