<template>
	<view style="height: 80vh;display: flex;align-items: center;justify-content: center;">
		<text @click="getNewForm()" class="tn-icon-camera-fill" style="font-size: 200rpx;color:#3D7EFF;"></text>
		<text v-if="!isLogin" @click="changePhone()" class="tn-icon-sequence" style="font-size: 50rpx;color:#000;position: absolute;left: 50rpx;top:30rpx"></text>
		<uni-popup ref="popup" type="center" :mask-click="false">
			<view class="verify">
				<view
					class="login__info__item__input tn-flex tn-flex-direction-row tn-flex-nowrap tn-flex-col-center tn-flex-row-left">
					<view class="login__info__item__input__left-icon">
						<view class="tn-icon-phone"></view>
					</view>
					<view class="login__info__item__input__content login__info__item__input__content--verify-code">
						<input v-model="phone" maxlength="20" placeholder-class="input-placeholder"
							:placeholder="'请输入手机号'" />
					</view>
				</view>
				<view
					class="login__info__item__input tn-flex tn-flex-direction-row tn-flex-nowrap tn-flex-col-center tn-flex-row-left">
					<view class="login__info__item__input__left-icon">
						<view class="tn-icon-safe"></view>
					</view>
					<view class="login__info__item__input__content login__info__item__input__content--verify-code">
						<input v-model="code" placeholder-class="input-placeholder" :placeholder="'请输入短信码'" />
					</view>
					<view class="login__info__item__input__right-verify-code" @tap.stop="getCode()">
						<tn-button backgroundColor="tn-bg-blue" fontColor="#FFFFFF" size="sm" padding="5rpx 10rpx"
							width="100%" shape="round">{{tips}}</tn-button>
					</view>
				</view>
				<view class="login__info__item__button tn-bg-blue tn-color-white" hover-class="tn-hover"
					:hover-stay-time="150" @tap="comfirm()">确认</view>
			</view>
		</uni-popup>
		<tn-verification-code
			ref="code"
			uniqueKey="login-demo-5"
			:seconds="60"
			@change="codeChange">
		</tn-verification-code>
		<uni-popup ref="popup1" type="center" :mask-click="false">
			<view class="verify" style="padding:40rpx;">
				<textarea name="" id="" cols="30" rows="10" v-model="content" style="background-color: #eee;padding: 20rpx;width: 100%;" placeholder="添加描述..."></textarea>
				<view class="login__info__item__button tn-bg-blue tn-color-white" hover-class="tn-hover"
					:hover-stay-time="150" @tap="saveForm()">确认</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	var checkOpenGPSService = require('../common/util.js').checkOpenGPSService
	export default {
		data() {
			return {
				phone: '',
				code: '',
				tips:'获取验证码',
				content:'',
				files:null,
				realId:'',
				realRid:'',
				edit_rid:'',
				edit_taskid:'',
				edit_nodeid:'',
				edit_key:'',
				edit_master:'',
				gpsInterval:null,
				opServer:'',
				isLogin:''
			}
		},
		methods: {
			getCode() {
				if (!this.$tn.test.mobile(this.phone)) {
					this.$tn.message.toast("请填写正确的手机号")
					return
				}
				if (this.$refs.code.canGetCode) { //图鸟组件自带的参数
					this.sendSms()
				} else {
					this.$tn.message.toast(this.$refs.code.secNum + '秒后再重试')
				}
			},
			sendSms(){
				let data={
					phoneNumber:this.phone
				}
				this.xAjax({
					url:"wxGetShotSms.php",
					data:data
				},(res)=>{
					if(res.ok){
						this.$refs.code.start()
					}else{
						this.$tn.message.toast("短信发送失败")
					}
				})
			},
			getNewForm(){
				this.address = ''
				this.xAjax({
					url: 'wxEdit.php?app=4&id=safeCasualPic&rid=',
					allwaysCall:true,
					hideErr:true
				}, (res) =>{
					if(res.err){
						if(!this.isLogin){
							this.getSession()
						}
						return
					}
					this.loadRes(res)
					this.getAddressName(this.longitude, this.latitude, (address)=>{
						this.address = address
					})
					this.takePhoto()
				})
			},
			changePhone(){
				uni.showModal({
					title:'提示',
					content:'是否切换手机号',
					success:(res)=>{
						if (res.confirm) {
							this.$refs.popup.open()
						}
					}
				})
			},
			loadRes(res){
				this.realId=res.realid
				this.realRid=res.realrid
				this.edit_rid=res.edit_rid
				this.edit_taskid=res.edit_taskid
				this.edit_nodeid=res.edit_nodeid
				this.edit_key=res.edit_key
				this.edit_master=res.edit_master
			},
			saveForm(){
				this.xAjax({
					url: 'wxSave.php',
					data: {
						id: 'safeCasualPic',
						edit_bid: 'editSave',
						edit_taskid: this.edit_taskid,
						edit_rid: this.edit_rid,
						edit_nodeid:this.edit_nodeid,
						edit_key: this.edit_key,
						edit_master: this.edit_master,
						CONTENTS:this.content,
						LATITUDE:this.latitude,
						LONGITUDE:this.longitude,
						PHONE_NUMBER:this.isLogin?'':this.phone,
						ADDRESS:this.address
					}
					
				}, (res)=>{
					this.uploadFile()
				})
			},
			takePhoto(){
				this.content = ''
				let phone = uni.getStorageSync('shotPhone')
				if(!phone && !this.isLogin){
					this.$refs.popup.open()
					return
				}
				// #ifdef MP-WEIXIN||APP-PLUS
				if (this.platform != 'ios' && this.platform != 'android' && this.platform != 'devtools' && this.platform != 'ohos') {
					this.showErr('只能使用手机拍照!')
					return
				}
				// #endif
				// #ifdef MP-WEIXIN||APP-PLUS
				if(!checkOpenGPSService()){
					this.showErr('请开启位置服务后再拍照!')
					return
				}
				// #endif
				if(!getApp().globalData.latitude){
					var that=this
					uni.showModal({
						title: '提示',
						content: '还未定位，要继续拍照吗?',
						success(c) {
							if (c.confirm) {
								that.chooseImage()
							}
						}
					})
				}else{
					this.chooseImage()
				}
			},
			chooseImage() {//选择图片上传
				let that = this
				uni.chooseImage({
					sourceType: ['camera'],
					sizeType: ['compressed'],
					success: function(res) {
						that.files = res.tempFiles
						that.$refs.popup1.open()
					},
					fail: function(err){
						console.log(err)
					}
				})
			},
			deleteForm(){
				let data = {
					id: 'safeCasualPic',
					rid: this.edit_rid,
					master: this.edit_master
				}
				let url = 'wxDelete.php'
				this.xAjax({
					url: url,
					data: data
				}, function(res) {
					
				})
			},
			uploadFile() {
				this.$refs.popup1.close()
				let files = this.files
				let that=this
				let url = this.opServer + '/wxUpload.php?uploadcol=PHOTO&id=' + this.realId + '&rid=' + this.realRid
					 + '&taskid=' + this.edit_taskid + '&nodeid=' + this.edit_nodeid + '&KEY=' + this.edit_key +'&master=' + this.edit_master
				if (that.latitude) { //可以用微信小程序取到经纬度
					url += '&latitude=' + that.latitude + '&longitude=' + that.longitude + '&accuracy=' + that.accuracy
					if(that.address){
						url += '&address=' + that.address
					}
					that.uploadFileSend(files, url)
				}else{
					this.uploadFileSend(files, url)
				}
			},
			uploadFileSend( files,url) {
				let that = this
				var formData = {}
				for (let i = 0; i < files.length; i++) {
					if (files[i].name) {
						formData = {
							'filename': files[i].name
						}
					} else if (files[i].duration) {
						formData = {
							'duration': files[i].duration
						}
					} else {
						formData = {}
					}
					uni.showLoading({
						title: '正在上传...'
					})
					//#ifdef APP-PLUS||H5
					var header={}
					//#endif
					// #ifdef MP-WEIXIN
					var header={'Cookie': uni.getStorageSync('cookieKey')}
					// #endif
					uni.uploadFile({
						url: url, 
						dataType: 'json',
						header: header,
						filePath: files[i].path,
						name: 'upload',
						formData: formData,
						success: (uploadRes)=> {
							uni.hideLoading()
							if (uploadRes.statusCode != 200) {//上传失败(应用未正常返回)且不是缓存的照片需要缓存
								uni.showModal({
									content: '上传失败:' + uploadRes.errMsg
								})
							} else {
								let res = JSON.parse(uploadRes.data)
								if (res.err) {
									uni.showModal({
										title: '提示',
										showCancel: false,
										content: res.err
									});
									this.deleteForm()
									return
								}
								if (res.name.alert) {
									uni.showModal({
										title: '提示',
										showCancel: false,
										content: res.name.alert
									});
								}
								if (res.state == 1) {
									uni.showModal({
										content: '上传成功'
									})
								} else {//上传失败(应用有返回但返回失败)且不是缓存的照片需要缓存
									uni.showModal({
										content: '上传失败:' + res.err
									})
									this.deleteForm()
								}
							}
						},
						fail: (info)=> {//上传失败(网络层)且不是缓存的照片需要缓存
							uni.hideLoading()
							uni.showModal({
								content: '上传失败:' + info.errMsg
							})
							this.deleteForm()
						}
					})
				}
			},
			comfirm(){
				if (!this.$tn.test.mobile(this.phone)) {
					this.$tn.message.toast("请填写正确的手机号")
					return
				}
				if (!this.code) {
					this.$tn.message.toast("请填写正确的短信码")
					return
				}
				uni.login({
					provider: 'weixin',
					success: (res) => {
						let weixinCode = res.code
						let data = {
							phone:this.phone,
							wxCode:weixinCode,
							smsCode:this.code
						}
						this.xAjax({
							url:"wxVerifyShotSms.php",
							data:data
						},(res)=>{
							if(res.userid){
								uni.setStorageSync('shotPhone',this.phone)
								uni.setStorageSync('shotMd5',res.shotMd5)
								this.$refs.popup.close()
							}else{
								this.$tn.message.toast("验证失败")
							}
						})
					},
					fail: (err) => {
						this.$tn.message.toast('微信登录失败')
					}
				})
			},
			getSession(){
				if(uni.getStorageSync('shotPhone')&&uni.getStorageSync('shotMd5')){
					uni.login({
						provider: 'weixin',
						success: (res) => {
							let weixinCode = res.code
							let data = {
								phone:this.phone,
								wxCode:weixinCode,
								shotMd5:uni.getStorageSync('shotMd5')
							}
							this.xAjax({
								url:"wxVerifyShotSms.php",
								data:data,
								hideErr:true,
								allwaysCall:true
							},(res)=>{
								if(!res.userid){
									this.$tn.message.toast("验证失败")
									this.$refs.popup.open()
								}else{
									this.getNewForm()
								}
							})
						},
						fail: (err) => {
							this.$tn.message.toast('微信登录失败')
						}
					})
				}else{
					this.$refs.popup.open()
				}
			},
			codeChange(event) {
				this.tips = event
			}
		},
		onLoad(e) {
			this.isLogin = e.login
			this.opServer = uni.getStorageSync('currServerUrl')
			if(!this.isLogin){
				if (!uni.getStorageSync('shotPhone')) {
					this.$refs.popup.open()
				}else{
					this.phone = uni.getStorageSync('shotPhone')
				}
			}
		},
		onReady() {
			let that = this
			getApp().globalData.gpsNeed = 1
			this.gpsInterval = setInterval(function() {
				if (!that.latitude) {
					that.latitude = getApp().globalData.latitude
					that.longitude = getApp().globalData.longitude
				}
			}, 1000)
		},
		beforeDestroy() {
			getApp().globalData.gpsNeed = 0
			clearInterval(this.gpsInterval);
			this.gpsInterval = null;
		}
	}
</script>
<style lang="scss" scoped>
	.verify {
		width: 600rpx;
		padding-bottom: 20rpx;
		margin-bottom: 200rpx;
		background-color: #fff;
		border-radius: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	@import '@/static/css/templatePage/custom_nav_bar.scss';

	.login {
		position: relative;
		height: 100%;
		z-index: 1;

		/* 背景图片 start */
		&__bg {
			z-index: -1;
			position: fixed;

			&--top {
				top: 0;
				left: 0;
				right: 0;
				width: 100%;

				.bg {
					width: 750rpx;
					will-change: transform;
				}
			}

			&--bottom {
				bottom: -10rpx;
				left: 0;
				right: 0;
				width: 100%;
				// height: 144px;
				margin-bottom: env(safe-area-inset-bottom);

				image {
					width: 750rpx;
					will-change: transform;
				}
			}
		}

		/* 背景图片 end */

		/* 内容 start */
		&__wrapper {
			// margin-top: 300rpx;
			width: 100%;
		}

		/* 切换 start */
		&__mode {
			position: relative;
			margin: 0 auto;
			width: 476rpx;
			height: 77rpx;
			background-color: rgba(255, 255, 255, 0.6);
			box-shadow: 0rpx 10rpx 50rpx 0rpx rgba(0, 3, 72, 0.1);
			border-radius: 39rpx;

			&__item {
				height: 77rpx;
				width: 100%;
				line-height: 77rpx;
				text-align: center;
				font-size: 30rpx;
				color: #080808;
				letter-spacing: 0em;
				text-indent: 0em;
				z-index: 2;
				transition: all 0.4s;

				&--active {
					font-weight: bold;
					color: #FFFFFF;
				}
			}

			&__slider {
				position: absolute;
				height: inherit;
				width: calc(476rpx / 2);
				border-radius: inherit;
				box-shadow: 0rpx 18rpx 72rpx 18rpx rgba(0, 195, 255, 0.1);
				z-index: 1;
				transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
			}
		}

		/* 切换 end */

		/* 登录注册信息 start */
		&__info {
			margin: 0rpx 30rpx 10rpx 30rpx;
			padding-bottom: 0;
			border-radius: 20rpx;

			&__item {

				&__input {
					margin-top: 59rpx;
					width: 90%;
					height: 77rpx;
					border: 1rpx solid #E6E6E6;
					border-radius: 39rpx;

					&__left-icon {
						width: 10%;
						font-size: 44rpx;
						margin-left: 20rpx;
						color: #838383;
					}

					&__content {
						width: 80%;
						padding-left: 10rpx;

						&--verify-code {
							width: 56%;
						}

						input {
							font-size: 24rpx;
							// letter-spacing: 0.1em;
						}
					}

					&__right-icon {
						width: 10%;
						font-size: 44rpx;
						margin-right: 20rpx;
						color: #838383;
					}

					&__right-verify-code {
						width: 34%;
						margin-right: 20rpx;
					}
				}

				&__button {
					margin-top: 75rpx;
					margin-bottom: 39rpx;
					width: 90%;
					height: 77rpx;
					text-align: center;
					font-size: 31rpx;
					font-weight: bold;
					line-height: 77rpx;
					letter-spacing: 1em;
					text-indent: 1em;
					border-radius: 39rpx;
					box-shadow: 1rpx 10rpx 24rpx 0rpx rgba(60, 129, 254, 0.35);
				}

				&__tips {
					margin: 30rpx 0;
					color: #AAAAAA;
				}
			}
		}

		/* 登录注册信息 end */

		/* 登录方式切换 start */
		&__way {
			margin: 0 auto;
			margin-top: 110rpx;

			&__item {
				&--icon {
					width: 85rpx;
					height: 85rpx;
					font-size: 80rpx;
					// border-radius: 100rpx;
					margin-bottom: 18rpx;
					position: relative;
					z-index: 1;
				}
			}
		}

		/* 登录方式切换 end */
		/* 内容 end */
	}

	.t-f {
		position: absolute;
		bottom: 200rpx;
		width: 100%;
		text-align: center;
		color: #666;
	}

	.t-e {
		position: absolute;
		bottom: 50rpx;
		text-align: center;
		width: 100%;
	}

	.sms-dialog {
		height: 300rpx;
		width: 700rpx;
		border-radius: 30rpx;

		&-box {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			margin: 0 20rpx;
			border-radius: 20rpx;

			&-input-box {
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				margin-top: 50rpx;
				border-radius: 39rpx;
				width: 90%;
				height: 77rpx;
				border: 1rpx solid #E6E6E6;
			}

			&-btn {
				margin-top: 50rpx;
				width: 90%;
				height: 77rpx;
				text-align: center;
				font-size: 31rpx;
				font-weight: bold;
				line-height: 77rpx;
				letter-spacing: 1em;
				text-indent: 1em;
				border-radius: 39rpx;
				background-color: #3D7EFF;
				color: white
			}
		}
	}
</style>