<template>
    <view>
        <web-view :src="linkURL"></web-view>
    </view>
</template>

<script>
	export default {
		data() {
			return {
				linkURL: ''
			}
		},
		methods: {
			
		},
		onLoad(e) {
			console.log(e)
			let url = decodeURIComponent(e.url)
			console.log(url)
			if(url.substr(0,4).toLowerCase()!='http'){
				if(url.substr(0,1)!='/'){
					url='/'+url
				}
				url=uni.getStorageSync('currServerUrl')+url
				if(url.indexOf("?")<0)url+="?"
				url+="&sessionid="+uni.getStorageSync('cookieKey').split("=")[1]
				if(e.id)url+="&id="+e.id
				if(e.rid)url+="&rid="+e.rid
				if(e.master)url+="&master="+e.master
				this.linkURL=url
			} else {
				let that = this
				this.xAjax({url:'wxVerifyLinkURL.php', data:{url:url}}, function(res){
					if(res.verifyok==1) {
						that.linkURL = url
					}else{
						that.showErr("非法网址!")
					}
				})
			}
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: e.label||""
				});
			}, 2000);
		}
	}
</script>

<style>

</style>
