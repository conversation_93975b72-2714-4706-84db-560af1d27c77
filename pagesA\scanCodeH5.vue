<template>
	<view>
		<mumu-get-qrcode @success='qrcodeSucess' @error="qrcodeError"></mumu-get-qrcode>
	</view>
</template>

<script>
	import mumuGetQrcode from '@/uni_modules/mumu-getQrcode/components/mumu-getQrcode/mumu-getQrcode.vue'
	export default {
		components: {
			mumuGetQrcode
		},
		data() {
			return {

			}
		},
		methods: {
			qrcodeSucess(result) {
				// 获取页面栈
				let pages = getCurrentPages();
				// 上一个页面实例
				let prevPage = pages[pages.length - 2];
				// 设置数据
				uni.navigateBack();
				setTimeout(()=>{
					prevPage.dealScanData(result)
				},500)
			},
			qrcodeError(err) {
				console.log(err)
				uni.showModal({
					title: '摄像头授权失败',
					content: '摄像头授权失败，请检测当前浏览器是否有摄像头权限。',
					success: () => {
						uni.navigateBack({})
					}
				})
			}
		}
	}
</script>

<style>

</style>