<template>
	<view class="template-login">
		<view class="login">
		
			<!-- 输入框内容-->
			<view class="login__info tn-flex tn-flex-direction-column tn-flex-col-center tn-flex-row-center">
				<!-- 登录 -->
				<block>
					<view class="login__info__item__input tn-flex tn-flex-direction-row tn-flex-nowrap tn-flex-col-center tn-flex-row-left">
						<view class="login__info__item__input__left-icon">
							<view class="tn-icon-phone"></view>
						</view>
						<view class="login__info__item__input__content">
							<input v-model="account" :focus="accountFocus" @blur="accountBlur" maxlength="20" placeholder="请输入账号" />
						</view>
					</view>
            
					<view class="login__info__item__input tn-flex tn-flex-direction-row tn-flex-nowrap tn-flex-col-center tn-flex-row-left">
						<view class="login__info__item__input__left-icon">
							<view class="tn-icon-safe"></view>
						</view>
						<view class="login__info__item__input__content login__info__item__input__content--verify-code">
							<input v-model="smscode" maxlength="6" :focus="smscodeFocus" @blur="smscodeBlur" placeholder="请输入短信码" />
						</view>
						<view class="login__info__item__input__right-verify-code" @tap.stop="getCode">
							<tn-button backgroundColor="tn-bg-blue" fontColor="#FFFFFF" size="sm" padding="5rpx 10rpx" width="100%" shape="round">{{ tips }}</tn-button>
						</view>
					</view>
					
					<view class="login__info__item__input tn-flex tn-flex-direction-row tn-flex-nowrap tn-flex-col-center tn-flex-row-left">
						<view class="login__info__item__input__left-icon">
							<view class="tn-icon-password"></view>
						</view>
						<view class="login__info__item__input__content">
							<input v-model="password" :focus="passwordFocus" @blur="passwordBlur" placeholder="请输入新密码" />
						</view>
					</view>
					
				</block>
		
				<view @click="submitPassword" class="login__info__item__button tn-bg-blue tn-color-white" hover-class="tn-hover" :hover-stay-time="150">确认</view>
          
			</view>
		</view>
		<!-- 验证码倒计时 -->
		<tn-verification-code
			ref="code"
			:seconds="60"
			@change="codeChange">
		</tn-verification-code>
		
	</view>
</template>

<script>
	import JSEncrypt from '../static/js/jsencrypt.min.js'
	var en = new JSEncrypt()
	en.setPublicKey(
		"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC8HMr2CBpoZPm3t9tCVlrKtTmI4jNJc7/HhxjIEiDjC8czP4PV+44LjXvLYcSV0fwi6nE4LH2c5PBPEnPfqp0g8TZeX+bYGvd70cXee9d8wHgBqi4k0J0X33c0ZnW7JruftPyvJo9OelYSofBXQTcwI+3uIl/YvrgQRv6A5mW01QIDAQAB"
	)
	export default {
		data() {
			return {
				account: '',
				password: '',
				smscode: '',
				accountFocus: false,
				passwordFocus: false,
				smscodeFocus: false,
				weixinCode: '',
				tips: '获取短信码',
				smsSendStatus:false
			}
		},
		methods: {
			// 获取验证码
			getCode() {
				let that=this
				if(!this.account){
					this.$tn.message.toast("请输入账号")
					this.accountFocus=true
					return
				}
				this.smscode=''
				this.smscodeFocus=true
				if (this.$refs.code.canGetCode) {//图鸟组件自带的参数
					// #ifdef MP-WEIXIN
					uni.login({
						provider: 'weixin',
						success: (res) => {
							this.weixinCode = res.code
							this.sendSms()
						},
						fail: (err) => {
							alert(err)
						}
					})
					// #endif
					// #ifndef MP-WEIXIN
					this.sendSms();
					// #endif
				} else {
					this.$tn.message.toast(this.$refs.code.secNum + '秒后再重试')
				}
			},
			sendSms(){
				let that=this
				let data={
					userAccount: en.encrypt(this.account),
					weixinCode: this.weixinCode
				}
				this.xAjax({
					url:"wxGetSmsCode.php",
					data:data
				},function(res){
					if(res.ok){
						that.$refs.code.start()
						that.smsSendStatus=true
					}else{
						that.$tn.message.toast("短信发送失败")
					}
				})
			},
			// 获取验证码倒计时被修改
			codeChange(event) {
				this.tips = event
			},
			accountBlur(){
				this.accountFocus=false
			},
			passwordBlur(){
				this.passwordFocus=false
			},
			smscodeBlur(){
				this.smscodeFocus=false
			},
			submitPassword(){
				let that=this
				if(!this.smsSendStatus){
					this.$tn.message.toast("请先获取短信码")
					return
				}
				if(!this.password){
					this.$tn.message.toast("请输入新密码")
					this.passwordFocus=true
					return
				}
				if(!this.smscode||this.smscode.length!=6){
					this.$tn.message.toast("请输入6位短信码")
					this.smscodeFocus=true
					return
				}
				if( this.password.length < 8 ){
					this.$tn.message.toast("密码长度至少8位");
					return;
				}
				//字符种类个数判断
				var s=0;
				if( /[a-z]/.test(this.password) ){
					s++;
				}
				if( /[A-Z]/.test(this.password) ){
					s++;
				}
				if( /[0-9]/.test(this.password) ){
					s++;
				}
				if( /[^0-9a-zA-Z]/.test(this.password) ){
					s++;
				}
				if(s < 4){
					this.$tn.message.toast("密码必须大小写,数字,特殊字符4种组合");
					return;
				}
				let url='wxResetPassword.php?'
				this.xAjax({
					url:url,
					data:{
						newPwd:en.encrypt(this.password),
						smsCode:this.smscode,
					}
				},function(res){
					if(res.ok){
						that.$tn.message.toast("密码修改成功")
						that.smsSendStatus=false
						that.password=''
						that.smscode=''
					}else{
						that.$tn.message.toast("密码修改失败,返回信息不正确:("+res+")")
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>

  
	.login {
		position: relative;
		height: 100%;
		z-index: 1;

		&__info {
			margin: 30rpx 30rpx 10rpx 30rpx;
			padding-bottom: 0;
			border-radius: 20rpx;
      
			&__item {
        
				&__input {
					margin-top: 59rpx;
					width: 90%;
					height: 77rpx;
					border: 1rpx solid #E6E6E6;
					border-radius: 39rpx;
          
					&__left-icon {
						width: 10%;
						font-size: 44rpx;
						margin-left: 20rpx;
						color: #838383;
					}
	  
					&__content {
						width: 80%;
						padding-left: 10rpx;
		
						&--verify-code {
							width: 56%;
						}
            
						input {
							font-size: 24rpx;
							// letter-spacing: 0.1em;
						}
					}

					&__right-verify-code {
						width: 34%;
						margin-right: 20rpx;
					}
				}
        
				&__button {
					margin-top: 75rpx;
					margin-bottom: 39rpx;
					width: 90%;
					height: 77rpx;
					text-align: center;
					font-size: 31rpx;
					font-weight: bold;
					line-height: 77rpx;
					letter-spacing: 1em;
					text-indent: 1em;
					border-radius: 39rpx;
					box-shadow: 1rpx 10rpx 24rpx 0rpx rgba(60, 129, 254, 0.35);
				}
        
				&__tips {
					margin: 30rpx 0;
					color: #AAAAAA;
				}
			}
		}
	}

</style>
