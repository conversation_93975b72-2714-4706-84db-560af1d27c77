<template>
	<view style="max-height:100vh;width:750rpx;" @touchmove="handletouchmove" @touchstart="handletouchstart" @touchend="handletouchend">
		<view class="main-top" :style="[{ paddingTop: editData.edit_id?0:statusHeight + 'px',top:!editData.edit_id?0:'calc('+statusHeight + 'px + 70rpx)'}]">
			<!-- 顶部自定义标题 -->
			<view style="width:100%;padding-left:10rpx;align-items:center;height:70rpx;display: flex;flex-direction: row;justify-content: flex-start;">
				<uni-icons type="left" size="22" @click="goBack()"></uni-icons>
				<view style="margin:0 10rpx;font-size: 30rpx;">{{!fromCache?navigationBarTitle:(navigationBarTitle+'(离线)')}}</view>
				<!-- <text :class="showList?'tn-icon-reduce-square':'tn-icon-add-square'" style="font-size: 35rpx;right: 20rpx;position: absolute;" @click="showList = !showList"></text> -->
			</view>
		</view>
		<scroll-view scroll-y v-if="Object.keys(popvalues).length>0" style="z-index: 20;background: #fff;display: flex;justify-content: center;max-height: 250rpx;">
			<view style="display: flex;flex-wrap: wrap;padding: 10rpx;">
				<view v-for="(poprid,idx) in Object.keys(popvalues)" :key="idx" style="background: #fff; white-space: nowrap;margin-right: 10px;margin-top: 10rpx; border-radius: 10rpx;border: #ccc 1px solid;padding: 10rpx;" @click="$delete(popvalues,poprid)">
					{{popvalues[poprid].join('|')}}
				</view>
			</view>
		</scroll-view>
		<!-- 列表上面的搜索框 -->
		<!-- <view v-show="showList" style="background-color: #fff;position: sticky;z-index:10;" :style="{top:'calc('+statusHeight+'px + 70rpx + '+topButtonHeight+'px)'}"> topButtonHeight是表单顶部的tab按钮，因为向上拉回自动隐藏，因此列表搜索栏应上滑到顶部，这里取消此高度-->
		<view v-show="showList" style="background-color: #fff;position: sticky;z-index:10;" :style="{top:'calc('+statusHeight+'px + 70rpx'}">
			<button size="mini" type="primary" style="margin-left: 10rpx;" v-if="Object.keys(popvalues).length>0"  @click="selectDone">确认</button>
			<view style="background-color: #fff;display:flex;flex-direction:row;width:100%;justify-content:space-between;align-items: center;">
				<text @click="showGrid=!showGrid" class="tn-icon-menu" style="font-size:50rpx;padding:0 20rpx"></text>
				<!-- <uni-icons color="#000" size="25" type="list" style="padding:0 20rpx" @click="showGrid=!showGrid" /> -->
				<uni-search-bar ref="uniSearchBar" style="width:100%;"
					alwaysShowClearBtn
					cancelButton=none
					radius="50"
					:placeholder="placeholder"
					bg-color="#f2f1f5"
					@clickSearch="clickSearch">
					<template v-slot:clearIcon>
						<uni-icons v-if="showScanIcon" color="#000" size="25" type="scan" @click.stop.prevent.native="scanCode" />
					</template>
				</uni-search-bar>
				<!-- <text class="tn-icon-scan" @click="scanCode" style="font-size:50rpx;position:absolute;right:110rpx;"></text> -->
				<!-- <uni-icons color="#000" size="25" type="gear" style="padding:0 20rpx" @click="dzColumn" /> -->
				<text class="tn-icon-sequence-vertical" :style="{color:sortName?'#007aff':'#000'}" style="font-size: 50rpx;padding: 0 5px;"  @click="showSortListSelect=true"></text>
				<text v-if="!listProp.id" class="tn-icon-set" @click="dzColumn" style="font-size:50rpx;padding:0 10rpx"></text>
				<text v-if="fabShow&&listProp.id" class="tn-icon-add-circle" @click="addNewItem" style="font-size:50rpx;padding:0 10rpx"></text>
			</view>
			<uni-segmented-control
				v-if="showSwitchSegmented&&menuType!='1'&&switchSegmentedValues.length>0"
				:current="currentSwitchSegmented" 
				:values="switchSegmentedValues" 
				style-type="text"
				active-color="#007aff"
				@clickItem="onClickSwitchSegmented" 
			/>
			<uni-segmented-control
				v-if="showSegmented&&!menuType&&!showSwitchSegmented&&segmentedValues.length>0"
				:current="currentSegmented" 
				:values="segmentedValues" 
				style-type="text"
				active-color="#007aff"
				@clickItem="onClickSegmented" 
			/>
		</view>
		<!--列表常规样式,由于悬浮新建按钮的z-index是11，搜索栏用10，这里只能用9-->
		<view  v-show="showList" v-if="!showGrid" style="z-index:9;position: relative;flex-direction: row" :style="menuType&&menus.length>0?'display: flex;':''">
			<!-- <view v-if="cacheFlag&&listUpdateData instanceof Object&&Object.keys(listUpdateData).length!=0" @click="saveCacheList" style="width: 100rpx;z-index: 10;height: 100rpx;border-radius: 50rpx;background: #67C23A;display: flex;position: absolute;bottom: 50rpx;left: 20rpx;"><text style="font-size:50rpx;color: #fff;margin: auto;"  class="tn-icon-upload-simple"></text></view> -->
			<scroll-view scroll-y='true' class="cart-menu"  v-if="menuType&&menus.length>0" :style="{'height': 'calc(100vh - 46px - '+(statusHeight)+'px - 70rpx)'}">
				<view v-for="menu in menus" :class="{'select-menu':menu.id==selectedMenu}" @click="selectMenu(menu)">{{menu.value}}</view>
			</scroll-view>
			<scroll-view scroll-y='true' @scrolltolower="pullUpLoadMore" :style="{'height': 'calc(100vh - 46px - '+(statusHeight)+'px - 70rpx)',paddingTop:showSegmented&&menuType=='1'&&segmentedValues.length>0?'36px':0}">
				<uni-segmented-control
					v-if="showSegmented&&menuType=='1'&&segmentedValues.length>0"
					:current="currentSegmented" 
					:values="segmentedValues"
					style="position:fixed;width:600rpx;z-index: 10;"
					:style="{top:'calc(46px + '+(statusHeight)+'px + 70rpx)'}"
					style-type="text"
					active-color="#007aff"
					@clickItem="onClickSegmented" 
				/> 
				<!-- 如果记录数为0但是定义了列表弹出按钮或不基于记录的按钮，这里虚拟一条记录显示弹出按钮 -->
				<view v-if="hasTopButton" class="record-button-layout">
					<button size="mini" v-for="(btn,idx) in recordButton" v-if="btn.btnTopShow==1" :key="idx" class="record-button" @click.stop="recordButtonSubmit(btn)">{{btn.btnLabel}}</button>
				</view>
				<view v-if="status!='loading'&&listData.length==0" style="display:flex;width:100%;height:80vh;justify-content:center;align-items: center;">
					<tn-empty mode="data"></tn-empty>
				</view>
				<view v-for="(item,index) in listData" :key="item.id" :data-index="item.id">
					<view class="list-cell" :class="{'select-cell':popvalues[item.id]}" :style="item.style.row" @click="goDetail($event,item,index)">
						<view style="display: flex;flex-direction: column;position: relative;">
							<!-- 删除图标 --><!-- 注意这里阻止冒泡程序只能用native.stop，直接.stop不生效 -->
							<uni-icons v-if="item.del" @click.native.stop="deleteItem(item.id)" class="trash" type="trash" color="#777" size="25" :style="item.style.trash" />
							<view class="list-content" v-for="(col,idx) in item.data" :class="{'not-update':listUpdateData[index]&&listUpdateData[index][idx]!==undefined,'default-value':col.showDefault}" :key="idx" v-if="col.name!=listStyle.LB.name&&col.name!=listStyle.MB.name&&col.name!=listStyle.RB.name">
								<!-- 默认第一个是标题 -->
								<view v-if="idx==0&&!col.editor.type" class="list-title" :style="col.style">
									{{ col.value || "" }}
								</view>
								<!-- 右上角内容 -->
								<view v-else-if="col.name==listStyle.RT.name" class="list-content-RT" :style="col.style">
									{{ col.value || "" }}
								</view>
								<!-- 其他内容 -->
								<view style="display: flex;flex-direction: row;" v-else-if="col.name!=listStyle.LT.name&&col.name!=listStyle.RT.name&&col.name!=listStyle.LB.name&&col.name!=listStyle.MB.name&&col.name!=listStyle.RB.name" :style="col.style">
									<text>{{col.title}}<text class="tn-icon-help" v-if="col.help" @click.stop="showHelpTip(col)"></text>：</text>
									<template v-if="col.editor.type&&editable&&(item.disabled&&item.disabled[idx]!==true||!item.disabled)">
										<input v-if="col.editor.type=='text'" :data-edit-index="index+','+idx" v-model="col.value" @input="saveInputOldValue" @blur="inputSave($event,col)" style="border-bottom: #ccc 1rpx solid;">
										<tn-number-box v-if="col.editor.type=='counter'" :inputHeight="40" :data-edit-index="index+','+idx" :index="index+','+idx" @blur="counterSave($event,index,idx)" @minus="counterSave($event,index,idx)" @plus="counterSave($event,index,idx)" v-model="col.value" :min="1"></tn-number-box>
										<tn-radio-group v-if="col.editor.type=='radio'" v-model="col.value">
											<tn-radio @change="radioSave($event,index,idx)" :labelSize="25" borderColor="#bababa" v-for="option,opidx in col.editor.options" :data-edit-index="index+','+idx" :name="option.id" :key="opidx" labelColor='#777'>
											  {{option.value}}
											</tn-radio>
										</tn-radio-group>
										<text v-if="col.editor.type=='combo'" @click="showSelect(col)" :data-edit-index="index+','+idx">{{col.editor.kv[col.value]}}<text class="tn-icon-down-triangle" :data-edit-index="index+','+idx"></text></text>
										<text v-if="col.editor.type=='multiselect'" @click="showMultiSelect(col)" :data-edit-index="index+','+idx">{{ getMultiValue(col.value,col.editor.kv)}}<text class="tn-icon-down-triangle" :data-edit-index="index+','+idx"></text></text>
										<text v-if="col.editor.type=='date'" @click="showEditorDatePicker = true" :data-edit-index="index+','+idx">{{col.value}}<text class="tn-icon-calendar" :data-edit-index="index+','+idx"></text></text>
										<tn-radio-group v-if="col.editor.type=='checkbox'" v-model="col.value">
											<tn-radio @change="switchSave($event,index,idx)" :labelSize="25" borderColor="#bababa" :data-edit-index="index+','+idx" :name="col.editor.checkValue" labelColor='#777'>
											  是
											</tn-radio>
											<tn-radio @change="switchSave($event,index,idx)" :labelSize="25" borderColor="#bababa" :data-edit-index="index+','+idx" :name="col.editor.uncheckValue" labelColor='#777'>
											  否
											</tn-radio>
										</tn-radio-group>
									</template>
									<template v-else><text>{{col.value}}</text></template>
								</view>
							</view>
							<!-- 最下面一栏左边的当前处理人和中间的环节和右边的时间 -->
							<view class="list-content" v-for="(col,idx) in item.data" :key="idx" v-if="(col.name==listStyle.LB.name||col.name==listStyle.MB.name||col.name==listStyle.RB.name)">
								<view class="list-content" v-if="col.name==listStyle.LB.name">
									<view class="lb-tag" :style="'background-color:'+ colors[index%20]+ ';' + item.style.tag">
										{{(col.value+"").slice(-2)}}
									</view>
									<view :style="col.style">
										{{ col.value || "" }}
									</view>
								</view>
								<view v-else-if="col.name==listStyle.MB.name" class="list-content-MB" :style="col.style">
									{{ col.value || "" }}
								</view>
								<view v-else-if="col.name==listStyle.RB.name" class="list-content-RB" :style="col.style">
									{{ col.value || "" }}
								</view>
							</view>
							<!-- <view class="list-content" v-if="listType=='cart' ||listType=='add'" style="justify-content: flex-end;">
								<text v-if="listType=='cart'" @click.stop='addCart' style="font-size:40rpx" :style="{color:!added?'#007AFF':'#E83A30'}" :class="{'tn-icon-add-circle':!added,'tn-icon-reduce-circle':added}" :min="1"></text>
							</view> -->
							<!-- 缓存按钮 --><!-- 注意这里阻止冒泡程序只能用native.stop，否则不生效 -->
							<uni-icons v-if="ifCache&&!item.isCached" @click.native.stop="addCache(item, index)" class="cache" type="star" color="#777" size="25" :style="item.style.cache" />
							<uni-icons v-if="ifCache&&item.isCached" @click.native.stop="deleteCache(item, index)" class="cache" type="star-filled" color="orange" size="25" :style="item.style.cache" />
						</view>
						<!-- 基于记录按钮的处理 --><!-- 注意这里阻止冒泡程序只能用native.stop，否则不生效 -->
						<view v-if="recordButton.length>0" class="record-button-layout">
							<button size="mini" v-for="(btn,idx) in recordButton" :key="idx" class="record-button" @click.stop="recordButtonSubmit(btn,item)" :style="item.style.button">{{btn.btnLabel}}</button>
						</view>
					</view>
				</view>
				<!--加载更多-->
				<uni-load-more v-if="listData.length>0" iconType="circle" :status="status" />
			</scroll-view>
		</view>
		<!-- 悬浮新建按钮 -->
		<uni-fab ref="fab" v-if="fabShow&&!listProp.id" horizontal="right" direction="horizontal" @fabClick="addNewItem" />
		<!-- 列表表格样式 -->
		<view  v-show="showList" v-if="showGrid">
			<uni-table border :stripe="true" emptyText="暂无更多数据" >
				<!-- 表头行 -->
				<uni-tr>
					<uni-th width="40"></uni-th>
					<uni-th v-for="(title,idx) in coltitle" :width="idx==0?'160':'100'" stickyType="left" :sticky="idx==stickyIdx-1" align="center">{{title}}<u-icon @click="handleSticky(idx+1)" :customStyle="{marginTop:'-20px',marginLeft:'-10rpx'}" :color="idx==stickyIdx-1?'#3688ff':''" :name="idx==stickyIdx-1?'pushpin-fill':'pushpin'" size="15"></u-icon></uni-th>
				</uni-tr>
				<!-- 表格数据行 -->
				<uni-tr v-for="(row,index) in listData">
					<uni-td :sticky="0==stickyIdx" :stickyType="0==stickyIdx?'left':'left'" align="left">{{row.rank}}</uni-td>
					<uni-td v-for="(cell,idx) in row.data" :sticky="idx+1==stickyIdx" :stickyType="idx+1==stickyIdx?'left':'left'" align="left">{{cell.value}}</uni-td>
				</uni-tr>
			</uni-table>
		</view>
		<!-- 抽屉式搜索框 -->
		<uni-drawer ref="searchDrawer" mode="right" width="650" @close="closeDrawer('right')">
			<form @submit="formSubmit" @reset="formReset">
				<scroll-view scroll-y="true" style="height:calc(100vh - 90rpx); padding-left: 20rpx;margin-bottom: 90rpx;">
					<!--#ifdef H5-->
					<!-- H5下顶部不用预留高度110改成50,180改成120，但湖北H5环境是小程序，会遮挡，需要多留60的高，仍然需要改为110和180-->
					<view style="position:fixed;top:0;padding:110rpx 0 10rpx 50rpx;z-index:9999;width:100%;background-color: #fff;font-weight: 700;">设置筛选条件，右滑关闭</view>
					<view style="margin-top:180rpx;z-index:99;">
					<!--#endif-->
					<!--#ifndef H5-->
					<view style="position:fixed;top:0;padding:110rpx 0 10rpx 50rpx;z-index:9999;width:100%;background-color: #fff;font-weight: 700;">设置筛选条件，右滑关闭</view>
					<view style="margin-top:180rpx;z-index:99;">
					<!--#endif-->
						<view v-for="(sub, key) in searchData" :key="key">
							<!-- 输入栏 -->
							<view class="fttx-form-group" v-if="sub.type=='input'">
								<view class="title">{{sub.label}}</view>
								<input :name="sub.name" @confirm="searchConfirm" @input="onSearchInput(sub)" confirm-type="search" v-model="sub.value"/>
								<uni-icons v-if="sub.showClear" type="clear" style="position:absolute;right:25rpx;z-index:99" size="25" color="#999" @click="onClearSearchInput(sub)"></uni-icons>
							</view>
							<!-- 日期范围 -->
							<view class="fttx-form-group" v-else-if="sub.type=='calendar'">
								<view class="title">{{sub.label}}</view>
								<input style="width:100%;margin:0;" disabled=true @click="onShowDatePicker(sub,1)" :name="sub.name" v-model="sub.value"/>
								<view v-if="sub.value!=''" style="position:absolute;left:340rpx;z-index:99">
									<uni-icons color="#999" size="25" type="clear" @click="clearDate(sub,1)" />
								</view>-
								<input style="width:100%;margin:0;z-index:98" disabled=true @click="onShowDatePicker(sub,2)" :name="sub.name2" v-model="sub.value2"/>
								<view v-if="sub.value2!=''" style="position:absolute;right:25rpx;z-index:99">
									<uni-icons color="#999" size="25" type="clear" @click="clearDate(sub,2)" />
								</view>
							</view>
							<!-- 日期按天周月 -->
							<view class="fttx-form-group" v-else-if="sub.type=='day'">
								<view class="title">{{sub.label}}</view>
								<input style="width:100%;margin:0;" disabled=true @click="onShowDatePicker(sub,1)" :name="sub.name" v-model="sub.value"/>
								<view v-if="sub.value!=''" style="position:absolute;right:25rpx;z-index:99">
									<uni-icons color="#999" size="25" type="clear" @click="clearDate(sub,1)" />
								</view>
							</view>
							<!-- 下拉框 -->
							<view v-else-if="sub.type=='select'">
								<uni-collapse>
									<uni-collapse-item :border="false" :open="sub.open" :title="sub.label">
										<view class="optionContainer">
											<view v-if="sub.options.length>30" style="width:590rpx;margin-bottom:10rpx;">
												<input type="text" @input="filterOption(optionFilter[sub.name], sub.options)" v-model="optionFilter[sub.name]" class="option-filter" placeholder="输入关键字过滤选项">
											</view>
											<view v-for="(item, index) in sub.options" :key="index" v-if="!item.hide">
												<button :style="{width:sub.optionWidth+'rpx'}" @tap="clickOption(sub,item)" :class="{'optionBtn':!item.selected,'optionSelect':item.selected}" >{{item.text}}</button>
											</view>
											<input v-show="false" :name='sub.name' :value='sub.value' />
										</view>
									</uni-collapse-item>
								</uni-collapse>
							</view>
						</view>
					</view>
				</scroll-view>
				<view class="btnContainer">
					<button class="searchBtn" formType="reset" type="default" style="background-color: #3688ff;color: #FFFFFF;">清除</button>
					<button class="searchBtn" formType="submit" type="default" style="background-color: #67C23A;color: #FFFFFF;">提交</button>
				</view>
			</form>
		</uni-drawer>
		<!-- 日期插件 -->
		<mx-date-picker ref="dateTimePicker" :show="showDatePicker" format="yyyy-mm-dd" type="date" :value="dateValue" :show-tips="true" :show-seconds="true" @confirm="onDateSelected" @cancel="onDateCancel" />
		
		<!-- 删除时弹出的模态框 -->
		<!-- <tn-modal
		  v-model="modalShow"
		  :content="modalContent"
		  :button="modalButton"
		  @click="clickModalBtn"
		> -->
		<!--列表扫码后关联多个工单弹出选择栏-->
		<uni-popup ref="popup" background-color="#fff">
			<view style="height:600rpx;font-size: 30rpx;">
				<view style="text-align:center; font-weight: bold;padding:20rpx; border-bottom: 1rpx solid #ccc;">二维码关联了多个工单，点击进入</view>
				<view style="padding:20rpx; border-bottom: 1rpx solid #ccc;" v-for="(item,idx) in scanNavicateURL" :key="idx" @click="navigateToURL(item)">{{item.title}}</view>
			</view>
		</uni-popup>
		<tn-select v-model="showListSelect" valueName="id" labelName="value" mode="single" :defaultValue="selectDefaultValue" :list="selectOptions" @confirm="selectSave"></tn-select>
		<mx-date-picker ref="dateTimePicker1" :show="showEditorDatePicker" format="yyyy-mm-dd hh:ii:ss" type="datetime" :value="listData[selectListIdx]?listData[selectListIdx].data[selectColIdx+1]:''" :show-tips="true" :show-seconds="true" @confirm="listDateSave" @cancel="showEditorDatePicker=false" />
		<tn-select v-model="showSortListSelect" :defaultValue="sortDefaultValue" mode="multi-auto" :list="sortListOptions" @confirm="sortList"></tn-select>
		<multipleSelect label-name="value" v-model="showListMultiSelect" :data="selectOptions" :default-selected="selectDefaultValue" @confirm="multiSelectSave" value-name="id"></multipleSelect>
	</view>
</template>

<script>
	var khkv = require('../common/util.js').khkv
	var checkOpenGPSService = require('../common/util.js').checkOpenGPSService
	import MxDatePicker from '@/components/mx-datepicker/mx-datepicker.vue'
	import multipleSelect from "@/components/momo-multipleSelect/momo-multipleSelect.vue";
	import {
		mapState,
		mapMutations
	} from 'vuex';
	export default {
		components: {
			MxDatePicker,
			multipleSelect
		},
		props:{
			listProp: {
			  type: Object,
			  default:function(){return {}}
			},
			fromCache:{
				type:Boolean,
				default:false
			},
			editData:{
				type:Object,
				default:function(){return {}}
			},
			topButtonHeight:{
				type:Number,
				default:0
			},
		},
		created() {
			// #ifdef MP-WEIXIN
			this.statusHeight = uni.getMenuButtonBoundingClientRect().top
			// #endif
			// #ifdef APP-PLUS
			this.statusHeight = 40
			// #endif
			// #ifdef H5
			this.statusHeight = 0
			// #endif
		},
		data() {
			return {
				navigationBarTitle: '加载中...',
				scanNavicateURL: [],
				statusHeight:'',
				colors:[//tag颜色
					'#ffb34b', '#f2bba9', '#f7a196', '#f18080', '#88a867', '#bfbf39', '#89c152', '#94d554', '#f19ec2', '#afaae4', 
					'#e1b0df', '#c38cc1', '#72dcdc', '#9acdcb', '#77b1cc', '#448aca', '#86cefa', '#98d1ee', '#73d1f1', '#80a7dc'
				],
				showSegmented: false,
				showSwitchSegmented:false,
				tabQueryName: 'tab',
				segmentedValues: [],
				switchSegmentedValues:[],
				currentSegmented: 0,
				currentSwitchSegmented:0,
				tabQuery: [],
				switchPages:[],
				//列表参数
				id: '',
				label: '',
				master: '',
				ref: '',
				hasTopButton: false,
				popget: '',
				popwrite: '',
				popvalues:{},
				cols: '',
				filter: '',
				gpsNeed: 0,
				barcodeType: '',//
				barcodeURL:'',//扫码类型3扫码后调用后台程序
				barcodePrompt: '',//扫码后的提示语
				showScanIcon: false,
				placeholder: '左滑筛选-当前0个筛选',
				recordButton: [],//基于记录的按钮
				stickyIdx: 0,//固定的列序号
				dataURL: 'wxListData.php',
				listStyle: {},
				TLcss: '',
				TRcss: '',
				showList:true,
				BLcss: '',
				BRcss: '',
				coltitle: [],
				colname:[],
				coleditor:[],
				coldefault:[],
				searchData: [],
				//flowScope:'',
				listData:[],
				originListData:[],
				layout: {},
				layoutData:{},
				ifCache: false,//列表是否开启离线缓存
				//删除模态框按钮
				modalShow: false,
				modalContent: '',
				modalButton:[
					{
						text: '取消',
						backgroundColor: 'tn-bg-blue',
						fontColor: '#FFFFFF'
					},
					{
						text: '确定',
						backgroundColor: 'tn-bg-red',
						fontColor: '#FFFFFF'
					}
				],
				//搜索栏入参
				searchValue: '',
				showGrid: false,
				//左滑出搜索窗参数
				flag: 0,
				lastX: 0,
				lastY: 0,
				status: 'loading', //上拉加载状态判断
				selectRowId:'',
				fabShow:false,//悬浮按钮
				enableInsert: false,
				editable:false,
				//搜索栏日期
				showDatePicker: false,
				showEditorDatePicker: false,
				showSortListSelect:false,
				currCanlender:[],
				currCanlenderIdx:'',
				dateValue:'',
				optionFilter:{},
				listType:'',
				added:false,
				selectedMenu:'',
				listContentType:[{},{type:'input'},{},{type:'select',options:[]},{type:'counter'},{type:'radio',options:[]}],
				showListSelect:false,
				showListMultiSelect:false,
				selectOptions:[],
				selectListIdx:-1,
				selectColIdx:-1,
				listUpdateData:{},
				cacheFlag:false,
				totalCount:0,
				componentFlag:false,
				selectDefaultValue:[],
				sortListOptions:[],
				sortName:'',
				sortType:'',
				sortDefaultValue:[0,0],
				menuType:'',
				menus:[]
			}
		},
		watch:{//监听变量的值变化
			filter(value){
				if(!value){
					this.placeholder = '左滑筛选-' + '当前0个筛选'
				}else{
					var count = value.match(/filter/g).length
					this.placeholder = '左滑筛选-' + '当前'+count+'个筛选'
				}
			},
			listProp(e){
				this.loadData(e)
			},
			listUpdateData:{
				handler(val){
					if(!this.cacheLists){
						return
					}
					if(!this.cacheLists[this.userId][this.editData.edit_id][this.editData.edit_rid].updateData){
						this.cacheLists[this.userId][this.editData.edit_id][this.editData.edit_rid].updateData = {}
					}
					this.cacheLists[this.userId][this.editData.edit_id][this.editData.edit_rid].updateData[this.listProp.name] = val
					this.cacheLists[this.userId][this.editData.edit_id][this.editData.edit_rid].tabData[this.listProp.name].data.rows = this.listData
					uni.setStorageSync('cacheLists',this.cacheLists)
				},
				deep:true
			}
		},
		computed: {
			...mapState(['KV'])
		},
		methods: {
			...mapMutations(['setKV']),
			goBack: function(){
				uni.navigateBack()
			},
			navigateToURL: function(e){
				uni.navigateTo({
					url: e.url
				})
				this.$refs.popup.close()
			},
			filterOption: function(e,options){
				for(var i=0;i< options.length;i++){
					if(options[i].text.indexOf(e)<0){
						options[i].hide=true
					}else{
						options[i].hide=false
					}
				}
			},
			showSelect(col){
				setTimeout(()=>{
					this.selectOptions = col.editor.options
					for(let i=0;i<this.selectOptions.length;i++){
						if(col.value==this.selectOptions[i].id){
							this.selectDefaultValue = [i]
							break
						}
					}
					this.showListSelect = true
				})
			},
			showMultiSelect(col){
				setTimeout(()=>{
					this.selectOptions = col.editor.options
					this.selectDefaultValue = col.value.split(',')
					this.showListMultiSelect = true
				})
			},
			onClickSegmented(e){
				if(this.fromCache){
					uni.showToast({
						title:'离线模式下不可切换',
						icon:'none'
					})
					return
				}
				this.currentSegmented=e.currentIndex
				this.posStart = 0
				this.reload = true
				this.getListData()
			},
			onClickSwitchSegmented(e){
				this.currentSwitchSegmented=e.currentIndex
				this.posStart = 0
				this.reload = true
				this.loadData({id:this.switchPages[this.currentSwitchSegmented].id,label:this.switchPages[this.currentSwitchSegmented].value})
			},
			showHelpTip(item){
				uni.showModal({
					title:'帮助提示',
					content:item.help,
					showCancel:false
				})
			},
			selectDone(){
				let data = []
				for(let key in this.popvalues){
					let item = this.popvalues[key]
					for(let i=0;i<item.length;i++){
						if(!data[i]){
							data[i] = []
						}
						data[i].push(item[i])
					}
				}
				for(let i=0;i<data.length;i++){
					data[i] = data[i].join(',')
				}
				uni.$emit('bringPopValue', [this.popwrite,data.join('|') ])
				uni.navigateBack()
			},
			addNewItem(){
				if(!this.enableInsert){
					this.addRecord()
					return
				}
				let defaultData = {}
				let arrData = [this.listData[0]['data'][0]-1]
				let i = 0
				if (this.coldefault) {
					for(let key of Object.keys(this.coldefault)){
						if(this.coldefault[key]){
							defaultData[key] = this.coldefault[key]
						}
						arrData.push({value:this.coldefault[key],editor:this.coleditor[key],name:key,title:this.coltitle[i++]})
					}
				}
				this.xAjax({
					url: 'wxGridSave.php?id='+this.id+'&operation=insert&master='+this.master,
					data:defaultData
				}, (res) =>{
					this.listData.unshift({id:res.nid,data:arrData,del:this.listData[0].del,rank:this.listData[0]['rank']-1})
					this.posStart++
					this.navigationBarTitle = this.label + '-' + (++this.totalCount) + '行'
				})
			},
			onSearchInput(e){
				e.showClear=true
			},
			onClearSearchInput(e){
				// this.$set(e, "showClear", false)
				// this.$set(e, "value", '')
				e.showClear=false
				e.value=''
			},
			closeDrawer() {
				this.$refs.searchDrawer.close()
			},
			sortList(e){
				this.sortName = e[0].value
				this.sortType = e[1].value
				this.sortDefaultValue=[e[0].extra,e[1].extra]
				this.posStart = 0
				this.reload = true
				this.getListData()
			},
			scanCode(){//二维码扫描
				let that=this
				if(this.gpsNeed){
					// #ifdef MP-WEIXIN||APP-PLUS
					if(!checkOpenGPSService()){
						this.showErr("请先开启位置服务")
						return
					}
					//#endif
					if(!getApp().globalData.latitude){
						this.showErr("还未获取到定位信息")
						return
					}
				}
				// #ifdef MP-WEIXIN||APP-PLUS
				uni.scanCode({
					success: (res) => {
						this.dealScanData(res.result)
					}
				})
				//#endif
				// #ifdef H5
				uni.navigateTo({
					url:'/pagesA/scanCodeH5'
				})
				//#endif
			},
			dealScanData(res){
				let that = this
				if(res.indexOf("{")>-1){//表明是json
					that.scanParse(res)
					return
				}
				let url=res
				if(that.barcodeType==3){
					url=that.barcodeURL+'?id='+that.id+'&scanCode='+res
					if(that.master){
						url += '&master=' + that.master
					}
				}
				that.scanConfirm(that.barcodePrompt.replace("[]","["+res+"]"), that.barcodeType, url)
			},
			confirm() {
				// #ifndef APP-PLUS
				uni.hideKeyboard()
				// #endif
				// #ifdef APP-PLUS
				plus.key.hideSoftKeybord()
				// #endif
				let filter=''
				if(this.searchVal){
					filter='&filter['+this.keycol+']='+this.searchVal
				}
				this.pullDownToRefresh(filter)
			},
			getStyle(styleObj,item){
				let data = item.data
				if(!styleObj){
					styleObj = {}
				}
				function getValueByKey(name){
					for(let col of data){
						if(col.name==name){
							return col.value
						}
					}
					if(item.userdata[name]){
						return item.userdata[name]
					}
					return ''
				}
				let css = ''
				for(let key in styleObj){
					if(key!='name'){
						let val = styleObj[key]
						if(val.indexOf(':')>-1){
							val = getValueByKey(val.substring(1))
						}
						css += key + ':' + val + ';'
					}
				}
				return css
			},
			listSave(value){
				if(this.cacheFlag&&this.fromCache){
					if(!this.listUpdateData[this.selectListIdx]){
						this.$set(this.listUpdateData,this.selectListIdx,{})
					}
					this.listUpdateData[this.selectListIdx][this.selectColIdx] = value
					return
				}
				let postData = {id:this.id,data:{}}
				postData.data[this.listData[this.selectListIdx]['id']]=[{name:this.colname[this.selectColIdx],value:value}]
				this.listSaveData(postData,(res)=>{
					if(res.err){
						if(!this.listUpdateData[this.selectListIdx]){
							this.$set(this.listUpdateData,this.selectListIdx,{})
						}
						this.listUpdateData[this.selectListIdx][this.selectColIdx] = value
					}else{
						if(this.listUpdateData[this.selectListIdx]&&this.listUpdateData[this.selectListIdx][this.selectColIdx]!==undefined){
							delete this.listUpdateData[this.selectListIdx][this.selectColIdx]
						}
						this.listData[this.selectListIdx]['data'][this.selectColIdx].showDefault = false
					}
				},()=>{
					if(!this.listUpdateData[this.selectListIdx]){
						this.$set(this.listUpdateData,this.selectListIdx,{})
					}
					this.listUpdateData[this.selectListIdx][this.selectColIdx] = value
					if(!this.cacheFlag){
						uni.showModal({
							title:'提示',
							content:'保存失败,是否要离线保存?',
							success: (res) => {
								if(res.confirm){
									this.$emit('saveToCache')
									this.cacheFlag = true
								}
							}
						})
					}
				})
			},
			saveCacheList(){
				if(!(this.listUpdateData instanceof Object)||Object.keys(this.listUpdateData).length==0){
					return
				}
				let postData = {id:this.id,data:{}}
				for(let index in this.listUpdateData){
					for(let idx in this.listUpdateData[index]){
						if(!postData.data[this.listData[index]['id']]){
							postData.data[this.listData[index]['id']] = []
						}
						postData.data[this.listData[index]['id']].push({name:this.colname[idx],value:this.listUpdateData[index][idx]})
					}
				}
				this.listSaveData(postData,(res)=>{
					if(res.err){
						
					}else{
						this.listUpdateData = {}
						uni.showToast({
							title:'列表数据保存成功'
						})
						this.cacheFlag = false
					}
				},()=>{
					
				})
			},
			listSaveData(postData,callback,failCallback){
				let that = this
				postData['master'] = this.master
				this.xAjax({
					url: "wxListSave.php",
					data: JSON.stringify(postData),
					hideNetworkErr: true,
					hideMsg:true,
					allwaysCall: true,
					timeout: 10000
				},callback,failCallback)
			},
			saveInputOldValue(e){
				if (!e.detail.oldvalue) {
					e.detail.oldvalue = e.detail.value
				}
			},
			inputSave(e,item){
				if(item.ref){
					let isJson = false
					let checkArr = []
					try {
						checkArr = JSON.parse(item.ref)
						isJson = true
					} catch (e) {
						isJson = false
					}
					if(!isJson){
						// #ifndef MP-WEIXIN
						let checkFunc = eval(item.ref)
						let checkRes = checkFunc(item.value)
						if(checkRes){
							uni.showModal({
								title:'提示',
								content:checkRes.error||checkRes.alert
							})
							if(checkRes.error){
								item.value = ''
							}
						}
						// #endif
					}else{
						for(let checkItem of checkArr){
							let val = parseFloat(item.value)
							let numberBool = (checkItem.out&&(val<checkItem.range[0]||val>checkItem.range[1]))||(!checkItem.out&&((!checkItem.range[0]||val>=checkItem.range[0])&&(!checkItem.range[1]||val<=checkItem.range[1])))
							//let stringBool = (checkItem.string)&&(((!checkItem.check||checkItem.check=='in')&&(checkItem.range.includes(item.value)))||(checkItem.check=='out'&&(!checkItem.range.includes(item.value))))
							if(numberBool){
								uni.showModal({
									title:'提示',
									content:checkItem.error||checkItem.alert
								})
								if(checkItem.error){
									item.value = ''
								}
								break
							}
						}
					}
				}
				if (e.detail.value!=e.detail.oldvalue){
					e.detail.oldvalue = ""
					this.listSave(e.detail.value)
				}
			},
			radioSave(e,index,idx){
				this.selectListIdx = index
				this.selectColIdx = idx
				setTimeout(()=>{
					this.listData[this.selectListIdx].data[this.selectColIdx].value = e
					this.listSave(e)
				})
			},
			counterSave(e,index,idx){
				this.selectListIdx = index
				this.selectColIdx = idx
				clearTimeout(this.saveTimer)
				this.saveTimer = setTimeout(()=>{
					this.listSave(e.value)
				},200)
			},
			switchSave(e,index,idx){
				this.selectListIdx = index
				this.selectColIdx = idx
				setTimeout(()=>{
					this.listData[this.selectListIdx].data[this.selectColIdx].value = e
					this.listSave(e)
				})
			},
			multiSelectSave(e){
				let value = []
				for(let i=0;i<e.length;i++){
					value.push(e[i]['id'])
				}
				this.listData[this.selectListIdx].data[this.selectColIdx].value = value.join(',')
				this.listSave(value.join(','))
			},
			getMultiValue(value,kv){
				let datas = value.split(',')
				let values = []
				for(let i=0;i<datas.length;i++){
					values.push(kv[datas[i]])
				}
				return values.join(',')
			},
			selectSave(e){
				this.listData[this.selectListIdx].data[this.selectColIdx].value = e[0].value
				this.listSave(e[0].value)
			},
			listDateSave(e){
				this.showEditorDatePicker = false
				this.listData[this.selectListIdx].data[this.selectColIdx].value = e.value
				this.listSave(e.value)
			},
			clickOption(s,o) {
				this.$set(o,'selected',!o.selected)
				if(s.multi==1){//多选
					var v=[];
					for(let i=0;i< s.options.length;i++){
						if(s.options[i].selected){
							v.push(s.options[i].value)
						}
					}
					this.$set(s,'value',v);
				}else{//单选并且是选中的要取消原来的选项，还要判断是否有级联下级
					for(let i=0;i< s.options.length;i++){
						if(s.options[i].text!=o.text){
							this.$set(s.options[i],'selected',false)
						}
					}
					if(o.selected){
						this.$set(s,'value',o.value)
					}else{
						this.$set(s,'value','')
					}
					if(s.childcols){
						this.changeChildOptions(s.childcols)
					}
				}
			},
			changeChildOptions: function(e){
				let childcol=e.split(',')
				for(let i=0;i<childcol.length;i++){
					this.setChildOptions(childcol[i])
				}
			},
			setChildOptions: function(col){
				let that=this
				for(let i=0;i< this.searchData.length;i++){
					if(this.searchData[i].name==col){
						let url= "wxGetOption.php?id="+this.id+"&col="+col+khkv(this.searchData,this.searchData[i].filter)
						this.xAjax({url:url},function(res){
							let o=[]
							for(let k=0;k< res.options.length;k++){
								if(res.options[k][0]==''){
									res.options[k][1]='空白'
								}
								o[k]={"value":res.options[k][0],"text":res.options[k][1]}
							}
							that.$set(that.searchData[i],'options',o)
						},'hideLoading')
						if(this.searchData[i].childcols){
							this.changeChildOptions(this.searchData[i].childcols)
						}
						break
					}
				}
			},
			onShowDatePicker: function(e,i){
				this.currCanlender=e
				if(i==2){//选中的是第2个输入，这里打标签，选中后根据标签更新
					this.currCanlenderIdx=2
					this.dateValue=e.value2
				}else{
					this.currCanlenderIdx=1
					this.dateValue=e.value
				}
				this.showDatePicker=true
			},
			clearDate: function(e,i){
				if(i==2){
					this.$set(e,'value2','')
				}else{
					this.$set(e,'value','')
				}
				if(e.showValue){
					this.$set(e,'showValue','')
				}
			},
			onDateSelected: function(e){
				if(this.currCanlenderIdx==2){
					this.$set(this.currCanlender,'value2',e.value)
				}else{
					this.$set(this.currCanlender,'value',e.value)
				}
				this.showDatePicker=false
			},
			onDateCancel: function(e){
				this.showDatePicker=false
			},
			clickSearch() {
				this.$refs.searchDrawer.open();
			},
			dzColumn(){
				if(this.fromCache){
					uni.showToast({
						title:'离线模式下不可使用',
						icon:'none'
					})
					return
				}
				uni.navigateTo({
					url: 'dz?id='+this.id+'&cols='+this.cols
				})
			},
			addCart(){
				this.added = !this.added
			},
			formReset() {
				let len=this.searchData.length
				for(let i=0;i< len;i++){
					this.$set(this.searchData[i],'value','')
					if(this.searchData[i].type=='select'){
						for(let k=0;k< this.searchData[i].options.length;k++){
							this.$set(this.searchData[i].options[k],'selected',false)
						}
					}
				}
			},
			formSubmit(e) {
				this.closeDrawer()
				let filter=''
				let keys=Object.keys(e.detail.value)
				for(let i=0;i< keys.length;i++){
					if(e.detail.value[keys[i]]!=''){
						filter+='&filter['+keys[i]+']='+e.detail.value[keys[i]]
					}
				}
				this.pullDownToRefresh(filter)
			},
			searchConfirm(){
				this.closeDrawer()
				let filter=''
				let kv = {}
				let len = this.searchData.length
				for (let i = 0; i < len; i++) {
					if (this.searchData[i].value ) {
						filter+='&filter['+this.searchData[i].name+']='+ this.searchData[i].value
					}
				}
				this.pullDownToRefresh(filter)
			},
			addRecord() {
				let url='edit?id=' + this.id
				if(this.master){
					url+='&master='+this.master
				}
				uni.navigateTo({
					url: url
				})
			},
			addCache(e, index){//离线缓存列表记录用于无信号时访问表单
				this.$set(e, "isCached", true)
				var userId = getApp().globalData.userId
				var that = this
				var cacheLists = uni.getStorageSync('cacheLists')
				let url = this.editURL?this.editURL:'wxEdit.php' + '?id=' + this.id + '&rid=' + e.id + '&cache=1'
				if (this.master) {
					url += '&master=' + this.master
				}
				this.xAjax({
					url: url,
					hideLoading:true
				}, function(res) {
					if(cacheLists){
						if(cacheLists[userId]){
							if(!cacheLists[userId][that.id]){
								cacheLists[userId][that.id] = {label:that.label}
							}
						}else{
							cacheLists[userId] = {}
							cacheLists[userId][that.id] = {label:that.label}
						}
					}else{
						cacheLists = {}
						cacheLists[userId] = {}
						cacheLists[userId][that.id] = {label:that.label}
					}
					cacheLists[userId][that.id][e.id]={listData:e.data, formData:res, editUrl:url, colTitle:that.coltitle, colname:that.colname}
					let tabList = []
					for(let item of res.datas){
						if(item.type=='ZC'||item.type=='FORMTAB'){
							tabList.push(item)
						}
					}
					if(tabList.length>0){
						that.loadTabData(tabList,0,{},(tabData)=>{
							cacheLists[userId][that.id][e.id]={listData:e.data, formData:res, editUrl:url, colTitle:that.coltitle, colname:that.colname, tabData:tabData}
							uni.setStorageSync('cacheLists',cacheLists)
							uni.showToast({
								title : '已离线保存',
								duration: 2000
							})
						})
					}else{
						uni.setStorageSync('cacheLists',cacheLists)
						uni.showToast({
							title : '已离线保存',
							duration: 2000
						})
					}
				})
			},
			loadTabData(tabList,idx,tabData,callback){
				let item = tabList[idx]
				let url = 'wxList.php?id=' + item.id
				if (item.master) {
					url += '&master=' + item.master
				}
				this.xAjax({
					url: url
				}, (res) =>{
					let promises = [];
					if(!res.enableForm){
					tabData[item.name] = res
					if(tabList.length==++idx){
						callback(tabData)
					}else{
						this.loadTabData(tabList,idx,tabData,callback)
					}
					return
					}
					for(let row of res.data.rows){
						let p = new Promise((res)=>{
							let url = this.editURL?this.editURL:'wxEdit.php' + '?id=' + item.id + '&rid=' + row.id + '&cache=1'
							if (this.master) {
								url += '&master=' + this.master
							}
							this.xAjax({
								url: url,
								hideLoading:false
							}, (resData) =>{
								row.form = resData
								res()
							})
						})
						promises.push(p)
					}
					Promise.all(promises).then(()=>{
						tabData[item.name] = res
						if(tabList.length==++idx){
							callback(tabData)
						}else{
							this.loadTabData(tabList,idx,tabData,callback)
						}
					})
				})
			},
			deleteCache(e){
				this.$set(e, "isCached", false)
				var cacheLists = uni.getStorageSync('cacheLists')
				delete cacheLists[getApp().globalData.userId][this.id][e.id]
				uni.setStorageSync('cacheLists',cacheLists)
			},
			pullUpLoadMore() {
				if (this.fromCache || this.menuType == 2) return
				this.status = 'loading'
				this.getListData()
			},
			pullDownToRefresh(filter) {
				this.closeDrawer()
				this.filter = (filter == undefined) ? this.filter : filter
				this.posStart = 0
				this.reload = true
				this.getListData()
			},
			randomColorList(){
				this.colors.sort(() => Math.random() - 0.5);
			},
			saveListToCache(id){
				this.resData.data.rows = this.listData
				let cacheLists = uni.getStorageSync('cacheLists')
				cacheLists[this.userId][id][this.editData.edit_rid].tabData[this.listProp.name] = this.resData
				uni.setStorageSync('cacheLists',cacheLists)
			},
			initList() {
				let that = this
				this.randomColorList()
				// uni.setNavigationBarTitle({ //标题要先清空，避免获取服务器数据失败而显示之前页面的错误信息
				// 	title: that.label + '-加载中...'
				// })
				this.filter = ''
				this.reload = true
				this.posStart = 0
				if(this.fromCache||getApp().globalData.offline){
					try {
						var cacheLists = uni.getStorageSync('cacheLists')
						this.cacheLists = cacheLists
						this.cacheFlag = true
						uni.stopPullDownRefresh()
						if(cacheLists[this.userId][this.editData.edit_id][this.editData.edit_rid].updateData){
							this.listUpdateData = cacheLists[this.userId][this.editData.edit_id][this.editData.edit_rid].updateData[this.listProp.name]
						}
						this.loadListData(cacheLists[this.userId][this.editData.edit_id][this.editData.edit_rid].tabData[this.listProp.name])
					} catch {}
					return
				}
				let url = 'wxList.php?id=' + this.id
				if (this.master) {
					url += '&master=' + this.master
				}
				this.xAjax({
					url: url
				}, function(res) {
					that.loadListData(res)
				});
			},
			loadListData(res){
				let that = this
				if (res.err) {
					that.status = 'noMore'
					return
				}
				this.resData = res
				//权限按钮等处理
				if (res.barcodeType) {
					that.barcodeType = res.barcodeType
					that.barcodeURL = res.barcodeURL
					that.barcodePrompt = res.barcodePrompt
					that.showScanIcon = true
				}
				that.label = ''
				that.dataURL = 'wxListData.php'
				that.editURL = ''
				that.enableForm = ''
				that.targetid = ''
				that.targetidColumn = ''
				that.targetridColumn = ''
				that.ifCache = ''
				that.tabQuery= []
				that.currentSegmented=0
				that.segmentedValues = []
				that.tabQueryName = 'tab'
				that.listStyle= {}
				that.recordButton=[]
				that.fabShow=false
				that.filter = ''
				that.editable = false
				that.gpsNeed = 0
				if (!that.label) {
					that.label = res.label
				}
				if (res.dataURL) {
					that.dataURL = res.dataURL
				}
				if (res.editURL) {
					that.editURL = res.editURL
				}
				if(res.enableForm){
					that.enableForm = res.enableForm
				}
				if(res.targetid){
					that.targetid = res.targetid
				}
				if(res.targetidColumn){
					that.targetidColumn = res.targetidColumn
				}
				if(res.targetridColumn){
					that.targetridColumn = res.targetridColumn
				}
				if(res.ifCache){
					that.ifCache = true
				}
				if(res.tabQuery){//搜索栏下面的segmented流程选择分段
					that.showSegmented = true
					that.currentSegmented=0
					that.tabQuery=res.tabQuery
					for (let i = 0; i < res.tabQuery.length; i++) {
						if (res.tabQuery[i].select) {
							that.currentSegmented = i
						}
						that.segmentedValues.push(res.tabQuery[i].value)
					}
				}
				if (res.tabQueryName) {
					that.tabQueryName = res.tabQueryName
				}
				//列表自定义的样式
				that.listStyle={
					LT:{},
					RT:{},
					LB:{},
					MB:{},
					RB:{},
					row:{},
					button:{},
					trash:{},
					tag:{},
					cache:{}
				}
				if(res.listStyle){
					for(let key in res.listStyle){
						if(res.listStyle[key]){
							that.listStyle[key] = res.listStyle[key]
						}
					}
				}
				if(res.buttons){
					that.recordButton=res.buttons
				}
				if(res.editable){
					that.editable=true
				}
				if(res.enableAdd){
					that.fabShow=true
				} else if (res.enableInsert) {
					that.fabShow=true
					that.enableInsert=true
				}
				that.searchData = res.searchData
				if (res.defaultFilter) {
					that.filter = res.defaultFilter
				}
				
				if(res.switchPages&&that.switchPages.length==0){
					that.showSwitchSegmented = true
					that.currentSwitchSegmented=0
					that.switchPages=res.switchPages
					that.switchSegmentedValues = []
					for (let i = 0; i < res.switchPages.length; i++) {
						if(res.switchPages[i].id==that.id){
							that.currentSwitchSegmented=i
						}
						that.switchSegmentedValues.push(res.switchPages[i].value)
					}
				}
				if(that.menuType==''&&res.vertical){
					that.menuType = res.vertical
				}
				if(that.menuType=='1'){
					that.selectedMenu = that.id
					that.menus = that.switchPages
				}
				if(that.menuType=='2'||that.menuType=='3'){
					for(let item of that.tabQuery){
						if (item.select) {
							that.selectedMenu = item.id
						}
					}
					that.menus = that.tabQuery
				}
				//开启GPS定位
				if (res.GPS) {
					that.gpsNeed = 1
					getApp().globalData.gpsNeed=1
				}
				if(res.data){//第一次除了加载结构也加载数据，为了兼容版本，这里做了判断
					that.showListData(res.data)
				}else{
					that.getListData()
				}
			},
			getListData() {
				let that = this
				if (!this.reload) {
					this.status = 'loading'; //说明已有数据，目前处于上拉加载
				}
				let url = this.dataURL + '?id=' + this.id + '&posStart=' + this.posStart + "&cols=" + this.cols + "&master=" + this.master + this.filter;
				if(this.showSegmented&&this.tabQuery.length>0&&this.tabQuery[this.currentSegmented].id){
					url += "&filter[" + that.tabQueryName + "]=" + this.tabQuery[this.currentSegmented].id;
				}
				if(this.sortName){
					url += "&sort[" + this.sortName + "]=" + this.sortType;
				}
				if (this.ref) {
					var a = this.ref.split(",");
					for (var i = 0; i < a.length; i++) {
						var b = a[i].split("^");
						url += "&ref[" + b[0] + "]=" + b[1];
					}
				}
				this.xAjax({
					url: url
				}, function(res) {
					if (res.err) {
						that.status = 'noMore'
						return
					}
					that.listUpdateData = {}
					that.showListData(res)
				})
			},
			showListData(res){
				var that = this
				that.totalCount = res.total_count
				var userId = getApp().globalData.userId
				var cacheLists = uni.getStorageSync('cacheLists')
				if(cacheLists&&cacheLists[userId]){
					if(cacheLists[userId][that.id]){
						for(let i=0;i<res.rows.length;i++){
							if(cacheLists[userId][that.id][res.rows[i]['id']]){
								res.rows[i]['isCached'] = true
							}
						}
					}
				}
				let tempStyleCol = {}
				for(let key in that.listStyle){
					if(that.listStyle[key].name){
						tempStyleCol[that.listStyle[key].name] = that.listStyle[key]
					}
				}
				for(let i=0;i<res.rows.length;i++){
					for(let j=0;j<res.rows[i]['data'].length;j++){
						if(res.rows[i]['data'][j]['default']&&!res.rows[i]['data'][j]['value']){
							res.rows[i]['data'][j]['showDefault'] = true
							res.rows[i]['data'][j]['value'] = res.rows[i]['data'][j]['default']
						}
						if(tempStyleCol[res.rows[i]['data'][j]['name']]){
							res.rows[i]['data'][j].style = this.getStyle(tempStyleCol[res.rows[i]['data'][j]['name']],res.rows[i])
						}else if(that.listStyle[res.rows[i]['data'][j]['name']]){
							res.rows[i]['data'][j].style = this.getStyle(that.listStyle[res.rows[i]['data'][j]['name']],res.rows[i])
						}
					}
					res.rows[i].style = {}
					let rowArrStyle = ['row','button','tag','cache','trash']
					for(let rowKey of rowArrStyle){
						if(that.listStyle[rowKey]){
							res.rows[i].style[rowKey] = this.getStyle(that.listStyle[rowKey],res.rows[i])
						}
					}
				}
				let list = res.rows
				that.posStart += list.length
				that.coltitle = res.coltitle
				that.colname = res.colname
				that.coleditor = res.coleditor
				that.coldefault = res.coldefault
				that.sortListOptions = [{value: '',label: '默认',extra:0,children:[{value:'',label:'默认',extra:0}]}]
				for(let i=0; i<that.colname.length;i++){
					that.sortListOptions.push({value:that.colname[i],label:that.coltitle[i],extra:i+1,children:[{value:'asc',label:'升序',extra:0},{value:'desc',label:'降序',extra:1}]})
				}
				for(let i=0;i<res.rows.length;i++){
					if(!res.rows[i].coleditor){
						res.rows[i]['coleditor'] = that.coleditor
					}
				}
				that.listData = that.reload ? list : that.listData.concat(list)
				if (that.reload && list.length == 0) {
					that.isEmptyList = true
				}
				that.reload = false
				that.totalCount = res.total_count
				if(res.total_count==0){//有pop按钮没有记录的需要单独展示pop按钮，否则显示在每行记录上，另外不选记录的按钮也要显示，例如新插入记录的按钮
					for(let i=0; i< that.recordButton.length; i++){
						if(that.recordButton[i].btnTopShow == 1){
							that.hasTopButton=true
						}
					}
				}else{
					that.hasTopButton=false
				}
				uni.$emit(that.id, res.total_count)
				that.navigationBarTitle = that.label + '-' + res.total_count + '行'
				if (list.length == 0 || list.length == res.total_count) {
					that.status = 'noMore'
				}
			},
			selectMenu(menu){
				this.selectedMenu = menu.id
				if(this.menuType=='1'){
					this.posStart = 0
					this.reload = true
					this.loadData({id:menu.id,label:menu.value})
				}
				if(this.menuType=='2'||this.menuType=='3'){
					this.currentSegmented = this.tabQuery.indexOf(menu)
					if(this.fromCache||this.menuType=='2'){
						if(this.originListData.length==0){
							this.originListData = this.listData
						}
						this.getCacheFilterList(menu)
						return
					}
					this.posStart = 0
					this.reload = true
					this.getListData()
				}
			},
			getCacheFilterList(menu){
				if(menu.id===''){
					this.listData = this.originListData
					return
				}
				this.listData = this.originListData.filter((item)=>{
					return item.userdata[this.tabQueryName]===menu.id
				})
			},
			goDetail: function(event,e,idx) {
				if(event.target.dataset.editIndex){
					this.selectListIdx = parseInt(event.target.dataset.editIndex.split(',')[0])
					this.selectColIdx = parseInt(event.target.dataset.editIndex.split(',')[1]) 
					return
				}
				if (this.popget) { //弹窗带回向表单传值
					if(this.multiSelect){
						if(this.popvalues[e.id]){
							this.$delete(this.popvalues,e.id)
						}else{
							let col = this.popget.split(",")
							let value = []
							for(let i in col){
								value.push(e.userdata[col[i].toUpperCase()])
							}
							this.$set(this.popvalues,e.id,value)
						}
					}else{
						let col = this.popget.split(",")
						let value = []
						for(let i in col){
							value.push(e.userdata[col[i].toUpperCase()])
						}
						uni.$emit('bringPopValue', [this.popwrite, value.join("|")])
						uni.navigateBack()
					}
				} else if (this.enableForm) {
					if (e.userdata.WXURL) {//通过传参定义的链接有先处理
						uni.navigateTo({
							url: e.userdata.WXURL
						})
						return
					}
					if(this.targetidColumn){//通过列表定义的字段获取需要进入的表单所需模块id
						this.id=e.userdata[this.targetidColumn]
					}else if(this.targetid){
						this.id=this.targetid
					}
					getApp().globalData.cacheData = {listData:e.data,colTitle:this.coltitle,ifCache:this.ifCache,editURL:this.editURL?this.editURL:'wxEdit.php' + '?id=' + this.id + '&rid=' + e.id}
					let url = 'id=' + this.id + '&rid='
					if(this.targetridColumn){//通过列表定义的字段获取需要进入的表单所需主键
						url += e.userdata[this.targetridColumn]
					}else{
						url += e.id
					} 
					if (this.editURL) {
						url += '&editURL=' + this.editURL
					}
					if (this.master) {
						url += '&master=' + this.master
					}
					let navTo = "edit?" + url + "&ifCache=" + !!this.ifCache+ "&isCached=" + !!e.isCached
					if(e.form){
						getApp().globalData.formData = e.form
						uni.navigateTo({
							url: navTo
						})
					}else{
						this.xAjax({ //正常获取到表单数据后再进入表单,但需要屏蔽提示信息
							url: "wxEdit.php?" + url,
							hideMsg: true
						}, function(res) {
							if (!res.err) {
								getApp().globalData.formData = res
								uni.navigateTo({
									url: navTo
								})
							}
						})
					}
				}
			},
			deleteItem(rowId){
				let that=this
				uni.showModal({
					title: '提示',
					content: '确认要删除吗?',
					success: function(a) {
						if (a.confirm) {
							let data = {
								id: that.id,
								rid: rowId,
								master: that.master
							}
							let url = 'wxDelete.php'
							that.xAjax({
								url: url,
								data: data
							}, function(res) {
								if (res.rows) {
									for (let i = 0; i < that.listData.length; i++) {
										if (that.listData[i].id == res.rows[0]) {
											that.listData.splice(i, 1)
											return
										}
									}
								}
							})
						}
					}
				})
			},
			recordButtonSubmit(btn,item){//基于记录的按钮提交后程序，前面是按钮，后面是记录，未传入item则为非记录按钮
				let that = this
				if (btn.btnAction == 'delete') {
					this.deleteItem(item.id)
				} else if (btn.btnAction.indexOf('btn') >-1 ) {
					if (btn.btnConfirm) {
						uni.showModal({
							title: '提示',
							content: btn.btnConfirm,
							success: function(a) {
								if (a.confirm) {
									that.listBtnSubmit(btn,item)
								}
							}
						})
					} else {
						that.listBtnSubmit(btn,item)
					}
				} else if (btn.btnAction.indexOf('sca') >-1 ) {//扫码
					uni.scanCode({
						success: function(qrcode) {
							if(qrcode.result.indexOf("{")>-1){//表明是json
								that.scanParse(qrcode.result)
								return
							}
							if (!btn.btnHref) {
								uni.showModal({
									content: '扫码结果为:' + qrcode.result
								})
								return
							}
							let url = btn.btnHref+ '?id=' + that.id + '&rid=' + item.id + '&scanCode=' + qrcode.result
							if (that.edit_master) {
								url += '&master=' + that.edit_master
							}
							that.scanConfirm(btn.btnConfirm.replace(/\[\]/, '['+qrcode.result+']'), 3, url)
						}
					})
				} else if (btn.btnAction.indexOf('qrc') >-1 ) {//生码是基于记录的
					if (!item.userdata[btn.btnQueryURL]) {
						uni.showModal({
							title: '页面上没有找到需要生成二维码的内容!'
						})
						return false;
					}
					let url = "link?url="+encodeURIComponent("/phpqrcode/generateQrcode.php?t=" + Date.now() + "&qrContent=" + item.userdata[btn.btnQueryURL])
					uni.navigateTo({
						url: url
					})
				} else {//弹出窗口
					if (!btn.btnPopURL) {//btnPopURL是对像id中自定义的url，没有值则默认list，弹出通常是从子页面中弹出第二层子页面，需要获取第二层节点弹出条件，因此这里必须带上子页面的id和按钮类型
						let url = 'list?id=' + btn.btnHref
						if (this.master) {
							url += '&master=' + this.master + "-" + this.id + "-" + btn.btnAction
						}
						uni.navigateTo({
							url: url
						})
					} else {
						let that = this
						let KV = ''
						let url=btn.btnPopURL
						if (this.master) {//如果有主记录的需要翻译转换url中的字段值
							url += '&master=' + this.master
							if (btn.btnFilter) { //取主页面中表单的值
								let master = this.master.split('-')
								KV = '-' + this.id + '-' + btn.btnAction
								uni.$emit('khkv' + master[0] + master[1], btn.btnFilter) //edit中获取键值对，通过store获取过来后清空
								if (this.KV) {
									KV += '-' + encodeURIComponent(this.KV)
									url += KV
									this.setKV()
								}
							}
						}
						this.xAjax({//先要登记注册，访问对方系统先验证用户合法性
							url: 'wxPopRegister.php?id=' + that.id,
							data:{
								url:url
							}
						}, function(stamp) { 
							uni.navigateTo({
								url: 'link?label=' + btn.btnLabel + '&url=' + encodeURIComponent(stamp.url||url) + "&stamp=" + stamp.stamp
							})
						})
					}
				}
			},
			listBtnSubmit: function(btn,item) {
				let data = {
					do: btn.btnAction,
					id: this.id,
					master: this.master
				}
				if (btn.btnSelect == 1) {//基于记录的才有id，而执行按钮程序可以不基于记录
					data.rid = item.id
					if (btn.postData == 1) {//需要加工成与PC版一样的数据格式
						let row = []
						for (let k = 0; k < item.length; k++) {
							row[item[k].name] = item[k].value
						}
						data.data = JSON.stringify(row)
					}
				} else if (btn.postData == 1) {//需要加工成与PC版一样的数据格式
					let rows = []
					for (let i = 0; i< this.listData.length; i++) {
						let row = {}
						for (let k = 0; k < this.listData[i].data.length; k++) {
							row[this.listData[i].data[k].name] = this.listData[i].data[k].value
						}
						rows.push(row)
					}
					data.data = JSON.stringify(rows)
				}
				if (btn.btnCheckProc) {
					let that = this
					let url = 'wxListValidate.php'
					this.xAjax({
						url: url,
						data: data
					}, function(res) {
						if (res.confirm) {
							uni.showModal({
								title: '提示',
								content: res.confirm,
								success: function(a) {
									if (a.confirm) {
										that.listBtnProc(data,btn)
									}
								}
							})
						} else {
							that.listBtnProc(data,btn)
						}
					})
				} else {
					this.listBtnProc(data,btn)
				}
			},
			listBtnProc: function(data,btn) {
				let that = this
				let url = 'wxListProc.php?'
				if(this.master){
					url+="master="+this.master
				}
				this.xAjax({
					url: url,
					data: data
				}, function(res) {
					if(res.response){
						for (let i = 0; i < that.listData.length; i++) {
							if (that.listData[i].id == res.response.rid) {
								for(let j = 0; j < that.coltitle.length; j++){
									if (that.coltitle[j]== res.response.LABEL){
										that.$set(that.listData[i].data,j+1,res.response.value)
									}
								}
							}
						}
					}
					if(btn.btnEndAction){
						if (btn.btnEndAction == 1) { //1是关闭，2是刷新
							uni.navigateBack()
						} else if (btn.btnEndAction == 2) {
							let pages = getCurrentPages();
							let lastPage = pages[pages.length - 2]
							if (res.response) {
								if (lastPage.route == 'pages/edit') {
									that.$vm.responose(res) //传递回来的值需要去form表单页面设置值
								}
							}
							that.pullDownToRefresh(that.filter)
							//刷新上一个页面
							if(that.master.indexOf('-pop')>-1){
								lastPage.$vm.pullDownToRefresh()
							}
						} else if (btn.btnEndAction == 3) { //从列表中移除当前选中的记录,界面上还未定义3
							for (let i = 0; i < that.listData.length; i++) {
								if (that.listData[i].id == data.rid) {
									that.listData.splice(i, 1)
								}
							}
						}
					}
				})
			},
			changeNav(e){
				this.curNow = e
			},
			handleSticky(idx){
				if(this.stickyIdx==idx){
					this.stickyIdx = -1
				}else{
					this.showStickyAction =true
					this.tempstickyIdx = idx
				}
			},
			handletouchmove: function(event) {
				if (this.flag !== 0) {
					return;
				}
				let currentX = event.touches[0].pageX
				let currentY = event.touches[0].pageY
				let tx = currentX - this.lastX
				let ty = currentY - this.lastY
				if (Math.abs(tx) > Math.abs(ty)) { //左右方向滑动
					if (tx < 0) { //向左滑
						this.flag = 1
					} else if (tx > 0) { //向右滑
						this.flag = 2
					}
					if (tx < -7&&!this.showGrid) {
						this.$refs.searchDrawer.open()
					}
				} else { //上下方向滑动
					if (ty < 0) {
						this.flag = 3
					} else if (ty > 0) {
						this.flag = 4
					}
				}
				//将当前坐标进行保存以进行下一次计算
				this.lastX = currentX
				this.lastY = currentY
			},
			handletouchstart: function(event) {
				this.lastX = event.touches[0].pageX
				this.lastY = event.touches[0].pageY
			},
			handletouchend: function(event) {
				this.flag = 0
			},
			loadData(e){
				uni.$on('updateCacheState', (data)=> {
					for(let item of this.listData){
						if(item.id==data.rid){
							item.isCached = data.isCached
							break
						}
					}
				})
				if (e.id.indexOf("&") > -1) {//主从tab中定义的id可以带入参，这里需要处理
					e.id = e.id.split("&")[0]
				}
				this.listData = []
				this.id=e.id;
				this.label=e.label;
				if(e.master){
					this.master=e.master;
				}
				if(e.ref){
					this.ref=e.ref;
				}
				if(e.popget){
					this.popget=e.popget;
				}
				if(e.multi==='1'){
					this.multiSelect=true;
				}
				if(e.popwrite){
					this.popwrite=e.popwrite;
				}
				if(e.listURL){//加载数据
					this.listURL=e.listURL;
				}
				this.initList();
			}
		},
		mounted(){
			if(getApp().globalData.offline){
				this.userId = uni.getStorageSync('userId')
			}else{
				this.userId = getApp().globalData.userId
			}
			let e
			if(this.listProp.id){
				this.componentFlag = true
				e = this.listProp
			}else{
				return
			}
			this.loadData(e)
		},
		onLoad(e){
			this.loadData(e)
		},
		onReady() {
			let that=this
			uni.$on('refreshList',()=>{
				that.pullDownToRefresh()
			})
			uni.$on(this.id + 'dzSubmit', function(e) { //监听定制结果后下拉刷新
				that.sortName = ''
				that.sortType = ''
				that.cols = e
				that.sortDefaultValue = [0,0]
				that.pullDownToRefresh()
			})
			uni.$on('scanNavigateList', function(e) {//扫二维码后台返回多个记录数组需要弹出工单列表，点击后再跳转
				that.scanNavicateURL = e
				that.$refs.popup.open('bottom')
			})
		},
		onPullDownRefresh() {
		    this.pullDownToRefresh()
		},
		onReachBottom() {
			this.pullUpLoadMore()
		},
		onBackPress() {
			// #ifdef APP-PLUS
			plus.key.hideSoftKeybord();
			getApp().globalData.gpsNeed = 0
			// #endif
		},
		onUnload() {
			getApp().globalData.gpsNeed = 0
		}
	}
</script>
<style>
	page {
		min-height: 100%;
		background-color: #f2f1f5;
	}
</style>
<style lang="scss" scoped>
	view {
		line-height: inherit
	}
	
	.main-top {
		position: sticky;
		left: 0;
		z-index: 11;//这里只能用11，与悬浮新建按钮一致，搜索栏用10，列表显示用9
		width: 100%;
		background-color: #fafbfc;
	}
	
	.list-cell{
		position: relative;
		margin: 20rpx;
		margin-bottom: 0;
		padding: 20rpx;
		border-radius: 20rpx;
		background-color: #fff;
	}
	.select-cell{
		background-color: #c7e9ff;
	}
	//缓存按钮
	.cache{
		position:absolute;
		top:40rpx;
		right:0;
		margin-right:-5rpx;
		padding:10rpx 0 0 10rpx;
	}
	// 删除按钮
	.trash{
		position:absolute;
		bottom:30rpx;
		right:0;
		margin-right:-5rpx;
		padding:0 0 10rpx 10rpx;
	}
	.list-title{
		color: #000;
		//padding-right:30rpx;
		margin-bottom:15rpx;
		font-size: 35rpx;
		font-weight: bolder;
		max-width:600rpx;
		overflow:hidden;//省略号显示必须三个配合使用,white-space: nowrap;在后台配置动态加载
		text-overflow:ellipsis;
	}
	.list-content {
		display: flex;
		flex-direction: row;
		font-size: 28rpx;
		line-height: 45rpx;
		color: #777;
		overflow: hidden;
		&-RT{
			position: absolute;
			//margin-left:30rpx;
			right:0;
			top:0;
		}
		&-RB{
			position: absolute;
			right:0;
			bottom:0;
		}
		&-MB{
			position: absolute;
			left:280rpx;
			bottom:0;
		}
	}
	.record-button-layout{
		display: flex;
		flex-direction: row;
		font-size: 25rpx;
		width:100%;
		background-color: #fff;
		z-index: 5;
		flex-wrap: nowrap;
		justify-content: space-around;
		.record-button{
			background-color:#409EFF;
			color:#fff;
			width:100%;
			margin:10rpx;
			padding:0 !important;//按钮默认padding左右为14,容易引起跨行
			border-radius: 10rpx;
		}
	}
	//左下角的tag
	.lb-tag{
		border-radius:50%;
		color:white;
		background-color: green;
		width:45rpx;
		font-size: 22rpx;
		margin-right:15rpx;
		justify-content: center;
	}
	
	//抽屉式弹窗搜索栏
	.fttx-form-group {
		background-color: #fff;
		padding: 0;
		display: flex;
		align-items: center;
		height:90rpx;
		margin-right:20rpx;
	}
	
	.fttx-form-group .title {
		width: 180rpx;
		color: #303133;
		display: flex;
		align-items: center;
		margin-left:0rpx;
		padding-left:0rpx;
		padding-right: 20rpx;
		font-size: 27rpx;
		position: relative;
		height: 60rpx;
		font-weight: 700;
	}
	
	.fttx-form-group input {
		flex: 1;
		font-size: 25rpx;
		color: #555;
		height: 54rpx;
		padding-left: 27rpx;
		width:100%;
		margin-right:0;
		border-radius:27rpx ;
		background-color: #f2f1f5;
	}

	.btnContainer {
		display: flex;
		align-items: center;
		width:100%;
		flex: 1;
		flex-direction: row;
		position: fixed;
		bottom:0;
		background-color: #FFF;
		border-bottom-left-radius: 25rpx;
		height: 90rpx;
	}
	
	.optionContainer {
		display: flex;
		align-items: center;
		width:100%;
		flex: 1;
		flex-direction: row;
		flex-wrap: wrap;
	}
	
	.optionBtn {
		padding: 0rpx 2rpx;
		margin: 0 10rpx 20rpx 10rpx;
		line-height: 50rpx;
		height: 50rpx;
		font-size: 22rpx;
		border-radius: 25rpx;
		width:185rpx;
		background-color: #f2f1f5;
		border: 0.5px solid #f2f1f5;
		text-overflow: ellipsis;
		font-weight: 500;
	}
	
	.optionSelect {
		padding: 0rpx 2rpx;
		margin: 0 10rpx 20rpx 10rpx;
		line-height: 50rpx;
		height: 50rpx;
		font-size: 22rpx;
		border-radius: 25rpx;
		width:185rpx;
		background-color: #d9ecff;
		border:0.5px solid #3688ff;
		// color:#409EFF;
		color:#3688ff;
		text-overflow: ellipsis;
		font-weight: 500;
	}
	.not-update{
		background: #fde2e2 !important;
	}
	.search-scan{
		position: absolute;
		right: 110rpx;
	}
	
	.searchBtn {
		padding: 0 5px;
		height: 70rpx;
		font-size: 28rpx;
		border-radius: 50rpx;
		width:240rpx;
	}

	.option-filter {
		margin:0 50rpx 10rpx 10rpx;
		background-color:#fff;//#f6f5f9;
		border: 0.5rpx solid #aaa;
		width:100%;
		height:48rpx;
		border-radius: 27rpx;
		padding-left: 20rpx;
	}
	.cart-menu{
		width: 150rpx;
		display: flex;
		flex-direction: column;
		background: #fff;
		font-size: 24rpx;
	}
	.cart-menu  view{
		width: 100%;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		min-height: 50rpx;
		padding: 10rpx;
		padding-top: 20rpx;
		padding-bottom: 20rpx;
		text-align:center;
	}
	.select-menu{
		background: #f2f1f5;
		font-weight: 600;
	}
	.default-value{
		background: #c7e9ff;
	}
</style>
