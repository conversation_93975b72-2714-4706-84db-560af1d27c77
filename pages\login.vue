<template>
	<view>
		<view class="login">
			<!-- 随手拍图标 -->
			<!-- <text @click="goToShot" class="tn-icon-camera-fill" style="font-size: 60rpx;position: absolute;right: 50rpx;top:30rpx"></text> -->
			<!-- 顶部背景图片-->
			<!-- <view class="login__bg login__bg--top">
				<image class="bg" src="@/static/img/login-top2.png" mode="widthFix"></image>
			</view> -->
			<!-- 顶部logo图片-->
			<view style="display:flex;width:100%">
				<!-- 注意同步修改防护js文件、图片、顶部的随手拍图标和下面570行左右位置的服务器列表更新url，如果是H5则删除 @click="showPopup" -->
				<image src="@/static/img/fttx.svg" style="width:33%;margin:50rpx auto" @click="showPopup" mode="widthFix"></image>
				<!-- <image src="@/static/img/cxsj.svg" style="width:60%;margin:100rpx auto" @click="showPopup" mode="widthFix"></image> -->
				<!-- 已弃用原来的图标，采用FTTX的树形图标<image src="@/static/img/xnyq.svg" style="width:30%;margin:100rpx auto" @click="showPopup" mode="widthFix"></image> -->
				<!-- <image src="@/static/img/rzy.svg" style="width:60%;margin:100rpx auto" @click="showPopup" mode="widthFix"></image> -->
			</view>
			<view class="login__wrapper">
				<!-- 登录/注册切换 -->
				<view class="login-sussuspension login__mode tn-flex tn-flex-direction-row tn-flex-nowrap tn-flex-col-center tn-flex-row-center">
					<view class="login__mode__item tn-flex-1" :class="[{'login__mode__item--active': currentModeIndex === 0}]" @tap.stop="modeSwitch(0)">
					密码登录
					</view>
					<view class="login__mode__item tn-flex-1" :class="[{'login__mode__item--active': currentModeIndex === 1}]" @tap.stop="modeSwitch(1)">
					短信登录
					</view>
					<view class="login__mode__slider tn-cool-bg-color-15--reverse" :style="[modeSliderStyle]"></view>
				</view>
				<!-- 输入框内容-->
				<view class="login__info tn-flex tn-flex-direction-column tn-flex-col-center tn-flex-row-center">
					<!-- 登录 -->
					<view class="login__info__item__input tn-flex tn-flex-direction-row tn-flex-nowrap tn-flex-col-center tn-flex-row-left">
						<view class="login__info__item__input__left-icon">
							<view class="tn-icon-phone"></view>
						</view>
						<view class="login__info__item__input__content login__info__item__input__content--verify-code">
							<input v-model="account" :focus="accountFocus" @blur="accountBlur" maxlength="20" placeholder-class="input-placeholder" :placeholder="currentModeIndex === 0?'请输入账号':'请输入手机号'" />
						</view>
						<view class="login__info__item__input__right-verify-code" @tap="loginOffline">
							<tn-button backgroundColor="tn-bg-blue" fontColor="#FFFFFF" size="sm" padding="5rpx 10rpx" width="100%" shape="round">离线模式</tn-button>
						</view>
					</view>
					<view class="login__info__item__input tn-flex tn-flex-direction-row tn-flex-nowrap tn-flex-col-center tn-flex-row-left">
						<view class="login__info__item__input__left-icon">
							<view class="tn-icon-safe"></view>
						</view>
						<view class="login__info__item__input__content login__info__item__input__content--verify-code" style="display: flex; align-items: center;">
							<input style="flex-grow: 1;" v-model="password" :password="!showPassword" :focus="passwordFocus" @blur="passwordBlur" placeholder-class="input-placeholder" :placeholder="currentModeIndex === 0?'请输入密码':'请输入短信码'" />
							<view v-if="currentModeIndex === 0" @click="showPassword = !showPassword" style="font-size: 36rpx; padding-left: 20rpx;margin-right: 20rpx; color: #AAAAAA;">
							  <text :class="!showPassword ? 'tn-icon-eye-hide' : 'tn-icon-eye'"></text>
							</view>
						</view>
						<view v-if="currentModeIndex === 0" class="login__info__item__input__right-verify-code" @tap.stop="resetPwd">
							<tn-button backgroundColor="tn-bg-blue" fontColor="#FFFFFF" size="sm" padding="5rpx 10rpx" width="100%" shape="round">重设密码</tn-button>
						</view>
						<view v-if="currentModeIndex === 1" class="login__info__item__input__right-verify-code" @tap.stop="getCode()">
							<tn-button backgroundColor="tn-bg-blue" fontColor="#FFFFFF" size="sm" padding="5rpx 10rpx" width="100%" shape="round">{{ tips }}</tn-button>
						</view>
					</view>
				  <!--蓝色tn-bg-indigo tn-bg-blue -->
				  <view class="login__info__item__button tn-bg-blue tn-color-white" hover-class="tn-hover" :hover-stay-time="150" @tap="login()">登录</view>
				</view>
			</view>
		</view>
		<view style="margin:30rpx 80rpx;">特别说明：本程序非面向公众用户，仅针对内部用户管理使用，请向主管单位申请账号后再登录使用</view>
		<!-- 其他登录方式 -->
		<view v-if="hasProvider" class="t-f"><text>—— 第三方账号登录 ——</text></view>
		<view v-if="hasProvider" class="t-e">
			<image src="@/static/img/weixin.svg" @tap="loginOpenid()" style='z-index: 999;'></image>
		</view>
		<!-- 底部背景图片-->
		<!-- <view class="login__bg login__bg--bottom">
			<image src="@/static/img/login-bottom2.png" mode="widthFix"></image>
		</view> -->
		<!-- 密码登录的二次短信验证 -->
		<uni-popup ref="smsDialog" background-color="#fff" :isMaskClick="false">
			<view class="sms-dialog">
				<view style="position: absolute;top:0;right:0">
					<uni-icons color="#aaa" size="28" type="close" @click="closeSmsDialog" />
				</view>
				<view class="sms-dialog-box">
					<view class="sms-dialog-box-input-box">
						<view class="login__info__item__input__left-icon">
							<view class="tn-icon-safe"></view>
						</view>
						<view style="width:45%;padding-left:10rpx">
							<input v-model="smsConfirmCode" :focus="smsConfirmFocus" @blur="smsConfirmBlur" placeholder-class="input-placeholder" placeholder="请输入短信码" />
						</view>
						<view style="width:45%;margin-right:20rpx" @tap.stop="getCode(1)">
							<tn-button backgroundColor="tn-bg-blue" fontColor="#FFFFFF" size="sm" padding="5rpx 10rpx" width="100%" shape="round">{{ tips }}</tn-button>
						</view>
					</view>
					<view class="sms-dialog-box-btn" @tap="loginAccountBySms()">登录</view>
				</view>
			</view>
		</uni-popup>
		<!-- 手机绑定多个账号的需要选择账号 -->
		<uni-popup ref="accountList" background-color="#fff">
			<uni-section title="手机绑定了多个账号，请选择要登录的账号" type="line">
				<uni-list style="width:100%;margin:15rpx 0;border-radius: 20rpx;">
					<uni-list-item v-for="(item,idx) in accountList" :title="item" showArrow thumb="/static/img/mine.png" thumb-size="sm" clickable @click="loginSmsByAccount(item)" />
				</uni-list>
			</uni-section>
		</uni-popup>
		<!-- 验证码倒计时 -->
		<tn-verification-code
			ref="code"
			uniqueKey="login-demo-4"
			:seconds="60"
			@change="codeChange">
		</tn-verification-code>
		<!-- 模态框 -->
		<tn-modal
			v-model="showModal"
			:backgroundColor="backgroundColor"
			:width="width"
			:padding="padding"
			:radius="radius"
			:fontColor="fontColor"
			:fontSize="fontSize"
			:title="title"
			:content="content"
			:button="button"
			:showCloseBtn="closeBtn || !maskCloseable"
			:maskCloseable="maskCloseable"
			:zoom="zoom"
			:custom="custom"
			@click="clickBtn"
		>
		</tn-modal>
		<!--设置服务器域名-->
		<uni-popup ref="popup" background-color="#fff">
			<view style="height:400rpx">
				<view style="text-align: center;font-weight: bold;margin-top:20rpx">点击大图标进入此配置页面{{currVersion}}</view>
				<radio-group style="display:flex;margin:20rpx;flex-wrap:wrap;" @change="radioChange">
					<view style="display: flex;margin:10rpx 0;" v-for="(opt,index) in serverLists" :key="index">
						<label class="radio" style="min-width:100rpx;max-width:180rpx;">{{opt.operator}}
							<radio style="transform:scale(0.6)" :value="opt.operator" :checked="opt.operator==activeRadio"/>
						</label>
					</view>
					<view style="display: flex;position: fixed;bottom:10rpx;width:710rpx">
						<radio value="other" :checked="'other'==activeRadio" style="margin:10rpx;transform:scale(0.7)"/>
						<view style="width:100%;padding: 10rpx;">
							<input v-model="otherURL" @focus="focusOtherURL" @blur="inputOtherURL" type="text" placeholder="手动输入格式为https://域名:端口" style="border:solid 1px #27A1BA;">
						</view>
					</view>
				</radio-group>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import JSEncrypt from '../static/js/jsencrypt.min.js'
	import {soterAuth} from '../static/js/soterAuth.js'
	var en = new JSEncrypt()
	en.setPublicKey(
		"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC8HMr2CBpoZPm3t9tCVlrKtTmI4jNJc7/HhxjIEiDjC8czP4PV+44LjXvLYcSV0fwi6nE4LH2c5PBPEnPfqp0g8TZeX+bYGvd70cXee9d8wHgBqi4k0J0X33c0ZnW7JruftPyvJo9OelYSofBXQTcwI+3uIl/YvrgQRv6A5mW01QIDAQAB"
	)
	var en1 = new JSEncrypt()
	en1.setPublicKey(
		"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDFJyt1kbgdkKTscDoyCvsU3EHimuWB8Tc1HRkQ2dRb/Dg92Th8cxUQUVF6WF0+4IoaHleN0yB0AnhbDJhIzLF7y9btEG8jknS5jSa+MoZZKliRpl1/bU8L+p0/oTGESn+8zzrMD90Txft0QvbM6idDR+80F3RASA8ChLHXZqEKowIDAQAB"
	)
	en1.setPrivateKey("MIICXAIBAAKBgQDFJyt1kbgdkKTscDoyCvsU3EHimuWB8Tc1HRkQ2dRb/Dg92Th8cxUQUVF6WF0+4IoaHleN0yB0AnhbDJhIzLF7y9btEG8jknS5jSa+MoZZKliRpl1/bU8L+p0/oTGESn+8zzrMD90Txft0QvbM6idDR+80F3RASA8ChLHXZqEKowIDAQABAoGAL3PUFAI0zHjqGTaR60WVVVoGSaJ6pyIF5bTROasUX+d/KfPEkr+oTDsmX0oxd1bYJp0y+aHdZ87IZTHM3YjXFsY/lWDL4BdQxexFBOGoWnjDgWlCe2kXsgQauf9/C/hyT5XdgZhnqSCMuQ8uB7vB9yu5I4vuZBqVZvai5FpkU8ECQQDMNzUGj4DOLaihvwwTswHwG5y2Jo07QYOM16zjTDyLBYl1MLxgUQpNq0kZ0Q/YvmPu9ijY7obDS8UgnboSEk3zAkEA9yV8gOkyV/tq8UPHLMuMcPamHeKsSW/+c3jv5Nx+H8bukcC2wD4k7SqF6oy/NrWW1yJHLXuQikniif4PKFaMkQJAfQ2BhxbxRVBoZ0PaXWXM1f/SoyhQa+zeZazczgOtP2SQnBbQBBscOBuflBOVpKBSCc7rjIvkMkt/100+2DolSQJALbSpCc4WmV7NsECwnQAnwNxcbDp3Uj17+S7sbo64ZHgNtrbFFVhoy9OJYI8W7a83biHQuACaAnkXhB2QAA3BgQJBAIGmco36ovTf+uUAHVVipKNCCYjiTR7PSqqIGAp0IzRUbPqwbMO+aQvHsQLdCiFJ+xOlmsYmvTDf/A1bn8q25bA=")
	// import template_page_mixin from '@/libs/mixin/template_page_mixin.js'
	export default {
		// name: 'login-demo-4',
		// mixins: [template_page_mixin],
		data() {
			return {
				showPassword: false,
				accountFocus: false,
				passwordFocus: false,
				smsConfirmFocus: false,
				hasProvider: false,
				account:'',
				password:'',
				smsConfirmCode: '',
				weixinCode:'',
				title: '提醒',
				content: '',
				showModal: false,
				backgroundColor: '',
				width: '84%',
				padding: '',
				radius: 12,
				fontColor: '',
				fontSize: 0,
				button:[
				  {
					text: '确定',
					backgroundColor: 'tn-bg-indigo',
					fontColor: '#FFFFFF'
				  }
				],
				closeBtn: false,
				maskCloseable: true,
				zoom: true,
				custom: false,
				// 当前选中的模式
				currentModeIndex: 0,
				// 模式选中滑块
				modeSliderStyle: {
				  left: 0
				},
				// 倒计时提示文字
				tips: '获取短信码',
				currVersion: '',
				serverLists:[],
				activeRadio:'',
				otherURL:'',
				currServerUrl:'',
				//短信登录绑定的多账号需要选择
				accountList:[]
			}
		},
		watch: {
			currentModeIndex(value) {
				const sliderWidth = uni.upx2px(476 / 2)
				this.modeSliderStyle.left = `${sliderWidth * value}px`
			}
		},
		methods: {
			loginOffline(){
				var userId=uni.getStorageSync('userId')
				if(!userId){
					uni.showModal({
						content:"最近未登录过系统不能使用离线模式"
					})
					return
				}
				getApp().globalData.userId=uni.getStorageSync('userId')
				getApp().globalData.offline=true
				uni.reLaunch({
					url: 'mine',
				})
			},
			closeSmsDialog(){
				this.$refs.smsDialog.close()
			},
			accountBlur(){
				this.accountFocus=false
			},
			passwordBlur(){
				this.passwordFocus=false
			},
			smsConfirmBlur(){
				this.smsConfirmFocus=false
			},
			// 切换模式
			modeSwitch(index) {
				this.currentModeIndex = index
			},
			// 点击模态框按钮
			clickBtn(event) {
				this.showModal = false
			},
			goToShot(){//跳转到随手拍
				if (!uni.getStorageSync('currServerUrl')) {
					this.showPopup()
					return
				}
				uni.navigateTo({
					url:'/pagesA/shot'
				})
			},
			showPopup(){
				// #ifdef H5
				return//H5无需设置运营商
				// #endif
				let that=this
				// #ifdef APP-PLUS
				plus.runtime.getProperty(plus.runtime.appid, function(widgetInfo) {
					that.currVersion = "(当前版本:" + widgetInfo.version + ")"
				})
				// #endif
				this.getServerList(function(){
					that.serverLists=uni.getStorageSync('serverLists')
					let currServer=uni.getStorageSync('currServer')
					if(currServer){//已设置了服务端
						that.activeRadio=currServer.operator
					}
					that.$refs.popup.open('bottom')
				})
			},
			radioChange(e){
				this.activeRadio=e.detail.value
				if(e.detail.value!='other'){//选中清单内的直接设置url后关闭弹层
					for(var i in this.serverLists){
						if(e.detail.value==this.serverLists[i].operator){
							this.currServerUrl=this.serverLists[i].url
							uni.setStorageSync('currServerUrl',this.currServerUrl)
							uni.setStorageSync('currServer',this.serverLists[i])
						}
					}
					this.$refs.popup.close()
				}else if(this.otherURL!=''){//非清单内的url地址不为空的才关闭
					this.currServerUrl=this.otherURL
					uni.setStorageSync('currServerUrl',this.currServerUrl)
					uni.setStorageSync('currServer',{operator:'other'})
					this.$refs.popup.close()
				}
			},
			focusOtherURL(e){
				this.activeRadio='other'
				uni.setStorageSync('currServer',{operator:'other'})
				this.currServerUrl=e.detail.value
				uni.setStorageSync('currServerUrl',this.currServerUrl)
			},
			inputOtherURL(e){
				uni.setStorageSync('otherURL',e.detail.value)
				this.currServerUrl=e.detail.value
				uni.setStorageSync('currServerUrl',this.currServerUrl)
			},
			resetPwd() {
				if (!uni.getStorageSync('currServerUrl')) {
					this.showPopup()
					return
				}
				var url='/pagesA/resetPwd'
				if(this.account){
					url+="?account="+this.account
				}
				uni.navigateTo({
					url: url
				})
			},
			getWeixinCode(callback){
				if (!uni.getStorageSync('currServerUrl')) {
					this.showPopup()
					return
				}
				// #ifdef MP-WEIXIN
				uni.login({ 
					provider: 'weixin',
					success: (res) => {
						this.weixinCode = res.code
						if(callback)callback()
					},
					fail: (err) => {
						this.content='获取微信码失败:'+ err.errMsg
						this.showModal=true
					}
				})
				// #endif
				// #ifndef MP-WEIXIN
				if(callback)callback()
				// #endif
			},
			login(){
				if (this.account == ''){
					this.accountFocus=true
					if(this.currentModeIndex==0){
						this.$tn.message.toast("请输入账号")
					}else{
						this.$tn.message.toast("请输入手机号")
					}
					return
				}
				if(this.currentModeIndex==0){//账号密码登录
					this.loginAccount()
				}else{//短信码登录
					if(this.password.length!=6){
						this.passwordFocus=true
						this.$tn.message.toast("请输入6位验证码")
						return
					}
					this.loginSmscode()
				}
			},
			loginBySoterAuth(){
				let that = this
				soterAuth('login',()=>{
					that.sendLogin({
						url: "wxLoginAccount.php",
						data: {
							account : en.encrypt(en1.decrypt(uni.getStorageSync('soterAuth')['a'])) ,
							password : en.encrypt(en1.decrypt(uni.getStorageSync('soterAuth')['p']))
						}
					})
				})
			},
			loginAccount(){
				let that=this
				if(this.password == ''){ //账号密码不为空表示要重新绑定微信
					this.passwordFocus=true
					this.$tn.message.toast("请输入密码")
					return
				}
				this.getWeixinCode(function(){
					that.sendLogin({
						url: "wxLoginAccount.php",
						data: {
							weixinCode: that.weixinCode,
							account  : en.encrypt(that.account),
							password : en.encrypt(that.password)
						}
					})
					uni.setStorageSync('soterAuth',{a:en1.encrypt(that.account),p:en1.encrypt(that.password)})
				})
			},
			loginAccountBySms(){//双因子二次短信认证
				let that=this
				if(this.smsConfirmCode.length != 6){
					this.smsConfirmFocus=true
					this.$tn.message.toast("请输入6位短信码")
					return
				}
				that.sendLogin({
					url: "wxLoginAccountBySms.php",
					data:{
						smsCode : that.smsConfirmCode
					}
				})
			},
			loginSmscode(){
				let that=this
				if(this.password == ''){ //账号密码不为空表示要重新绑定微信
					this.passwordFocus=true
					this.$tn.message.toast("请输入短信码")
					return
				}
				this.getWeixinCode(function(){
					uni.removeStorageSync('soterAuth')
					that.sendLogin({
						url: "wxLoginSms.php",
						data: {
							weixinCode: that.weixinCode,
							smsCode : that.password
						}
					})
				})
			},
			loginSmsByAccount(e){//手机绑定多账号选中要登录的账号
				this.sendLogin({
					url: "wxLoginSmsByAccount.php",
					data:{
						userAccount: en.encrypt(e)
					}
				})
			},
			loginOpenid(hideAll){
				let that=this
				getApp().globalData.unLogin=true
				this.getWeixinCode(function(){
					that.sendLogin({
						url: "wxLoginOpenid.php",
						hideAll: hideAll,
						data:{
							weixinCode: that.weixinCode
						}
					})
				})
			},
			sendLogin(paras) {
				let that = this
				paras['timeout']=10000//登录无需等待默认的60秒，因为微信认证经常超时
				this.xAjax(paras, function(res) {
					if (res.expired) {
						uni.showModal({
							title: '提示',
							content: res.expired,
							success: function(a) {
								if (a.confirm) {
									that.resetPwd()
								}
							}
						})
					} else if(res.accountList){//短信登录手机绑定多个账号需要选择账号
						that.accountList=res.accountList
						that.$refs.accountList.open("bottom")
					}else if (res.loginsm) {//短信二次认证
						that.smsConfirmCode=''
						that.getCode(1)
					} else if (res.userid) {
						if(res.userName){
							getApp().globalData.userName = res.userName
							uni.setStorageSync('userName', res.userName)
						}
						if(res.userid){
							getApp().globalData.userId = res.userid
							uni.setStorageSync('userId', res.userid)
						}
						if(res.dept){
							getApp().globalData.dept = res.dept
							uni.setStorageSync('dept', res.dept)
						}
						if(res.isAdmin){
							getApp().globalData.isAdmin = res.isAdmin
							uni.setStorageSync('isAdmin', res.isAdmin)
						} else {
							uni.removeStorageSync('isAdmin')
						}
						if(res.bind){
							uni.showModal({
								title: '提示',
								content: "账号还未绑定手机号，绑定后可以重置密码和接收任务短信通知",
								success: function(a) {
									if (a.confirm) {
										uni.reLaunch({
											url: '/pagesA/bindMobile'
										})
									}
								}
							})
							return
						}
						//记录最后登录的用户id，离线模式时使用
						uni.setStorageSync('userId', res.userid)
						if (res.confirm) {//账号审计或密码即将过期的提示
							uni.showModal({
								title: '提示',
								content: res.confirm,
								success: function(a) {
									if (a.confirm) {
										that.getQuestionnaire()
									}
								}
							})
						} else {
							that.getQuestionnaire()
						}
					} else {
						uni.showModal({
							title: '登录失败',
							content: '返回信息不正确:(' + res + ')'
						})
					}
				})
			},
			getQuestionnaire() {//查看是否有问卷调查
				let url = 'wxGetQuestionnaire.php'
				this.xAjax({
					url: url,
					hideAll:true,
					allwaysCall:true
				}, (res) => {
					if(res.state&&res.state==1){
						uni.reLaunch({
							url: '/pagesA/question?data=' + JSON.stringify(res)
						})
					}else{
						uni.reLaunch({
							url: 'main',
						})
					}
				})
			},
			// 获取验证码
			getCode(smsConfirm) {
				if(!uni.getStorageSync('currServerUrl')){
					this.showPopup()
					return
				}
				let that=this
				if(!this.account&&!smsConfirm){//点击微信图标登录不验证
					this.$tn.message.toast("请输入手机号")
					this.accountFocus=true
					return
				}
				if(!smsConfirm){//短信二次验证不聚焦密码输入框
					this.password=''
					this.passwordFocus=true
				}
				if (this.$refs.code.canGetCode) {//图鸟组件自带的参数
					// #ifdef MP-WEIXIN
					uni.login({ 
						provider: 'weixin',
						success: (res) => {
							this.weixinCode = res.code
							this.sendSms(smsConfirm)
						},
						fail: (err) => {
							this.content=err
							this.showModal=true
						}
					})
					// #endif
					// #ifndef MP-WEIXIN
					this.sendSms(smsConfirm)
					// #endif
				} else {
					this.$tn.message.toast(this.$refs.code.secNum + '秒后再重试')
				}
			},
			sendSms(smsConfirm){
				let that=this
				let data={
					weixinCode: that.weixinCode
				}
				if(!smsConfirm){//双因子验证需要验证第一次登录的用户号码，无需提供手机号，其他的需要提供手机号码
					data.phoneNumber=this.account
				}
				this.xAjax({
					url:"wxGetSmsCode.php",
					data:data
				},function(res){
					if(res.ok){
						that.$refs.code.start()
						if(smsConfirm){
							that.$refs.smsDialog.open()//发送短信成功后弹出短信二次验证的对话框
							that.smsConfirmCode=''
							that.smsConfirmFocus=true
						}
					}else{
						that.$tn.message.toast("短信发送失败")
					}
				})
			},
			// 获取验证码倒计时被修改
			codeChange(event) {
				this.tips = event
			},
			//获取服务列表，加载时获取一次，点击图标也实时获取
			getServerList(callback){
				let that=this
				//注意改上面的图标
				//注意fttx引入hnsafe.js
				//注意如果打包h5时login放在main的下面，单点跳转直接进入。非h5时login放在最上面
				let url = 'https://www.fttx.cn:3721'
				// let url = 'https://yygl.cxsjidc.com:3721'//川西数据
				// let url = 'http://************:7100/v1'//西南石油
				//let url = 'http://************:9000/itsm'//itsm测试
				this.xAjax({
					url: url + '/app/getoperator.php',
					hideAll:true
				},function(res){
					if(res.operator){//获取到清单先更新到本地
						uni.setStorageSync('serverLists',res.operator)
					}
					if (that.otherURL=='') {//手动输入框默认使用当前服务器，避免获取不到清单时无法使用
						that.otherURL = url
					}
					if(callback){
						callback()
					}
				},function(){
					if (that.otherURL=='') {//手动输入框默认使用当前服务器，避免获取不到清单时无法使用
						that.otherURL = url
					}
					if(callback){
						callback()
					}
				})
			}
		},
		onReady() {
			// #ifdef MP-WEIXIN
			this.hasProvider=true
			// #endif
			
			// #ifdef H5
			uni.setStorageSync('currServerUrl','../')//H5无需制定ip
			// #endif
			
			// #ifdef MP-WEIXIN||APP-PLUS
			let currServer=uni.getStorageSync('currServer')
			this.activeRadio=currServer.operator
			this.otherURL=uni.getStorageSync('otherURL')
			//每次启动需要更新初始化配置
			let that = this
			this.getServerList(function(){
				let serverLists = uni.getStorageSync('serverLists')
				for (let i in serverLists) {
					if (serverLists[i]['operator'] == currServer.operator) {
						currServer = serverLists[i]
						uni.setStorageSync('currServer', currServer)
					}
				}
				if(currServer.operator=='other'){
					that.currServerUrl=that.otherURL
				}else{
					that.currServerUrl=currServer.url
				}
				if (currServer.locateInterval) {
					getApp().globalData.locateInterval = currServer.locateInterval
				}
				uni.setStorageSync('currServerUrl',that.currServerUrl)
			})
			// #endif
			// #ifdef APP
			if(uni.getStorageSync('isSoterAuth')&&uni.getStorageSync('soterAuth')&&!getApp().globalData.firstApp){
				getApp().globalData.firstApp = 1
				this.loginBySoterAuth()
			}
			// #endif
			// #ifdef MP-WEIXIN
			if(!getApp().globalData.unLogin){
				this.loginOpenid(true)
			}
			// #endif
		},
		onShareAppMessage(res) {
			return {
				path: '/pages/login'
			}
		}
	}
</script>

<style lang="scss" scoped>
@import '@/static/css/templatePage/custom_nav_bar.scss';
  /* 悬浮 */
  // .login-sussuspension{
  //   animation: suspension 3s ease-in-out infinite;
  // }
  
  // @keyframes suspension {
  //   0%, 100% {
  //     transform: translate(0 , 0);
  //   }
  //   50% {
  //     transform: translate(0rem , 1rem);
  //   }
  // }
  
	.login {
		position: relative;
		height: 100%;
		z-index: 1;
		
		/* 背景图片 start */
		&__bg {
			z-index: -1;
			position: fixed;
		  
			&--top {
				top: 0;
				left: 0;
				right: 0;
				width: 100%;
				
				.bg {
					width: 750rpx;
					will-change: transform;
				}
			}
		  
			&--bottom {
				bottom: -10rpx;
				left: 0;
				right: 0;
				width: 100%;
				// height: 144px;
				margin-bottom: env(safe-area-inset-bottom);
				
				image {
					width: 750rpx;
					will-change: transform;
				}
			}
		}
		/* 背景图片 end */
		
		/* 内容 start */
		&__wrapper {
		  // margin-top: 300rpx;
			width: 100%;
		}
		
		/* 切换 start */
		&__mode {
			position: relative;
			margin: 0 auto;
			width: 476rpx;
			height: 77rpx;
			background-color: rgba(255,255,255,0.6);
			box-shadow: 0rpx 10rpx 50rpx 0rpx rgba(0, 3, 72, 0.1);
			border-radius: 39rpx;
		  
			&__item {
				height: 77rpx;
				width: 100%;
				line-height: 77rpx;
				text-align: center;
				font-size: 30rpx;
				color: #080808;
				letter-spacing: 0em;
				text-indent: 0em;
				z-index: 2;
				transition: all 0.4s;
				
				&--active {
					font-weight: bold;
					color: #FFFFFF;
				}
			}
		  
			&__slider {
				position: absolute;
				height: inherit;
				width: calc(476rpx / 2);
				border-radius: inherit;
				box-shadow: 0rpx 18rpx 72rpx 18rpx rgba(0, 195, 255, 0.1);
				z-index: 1;
				transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
			}
		}
		/* 切换 end */
		
		/* 登录注册信息 start */
		&__info {
			margin: 0rpx 30rpx 10rpx 30rpx;
			padding-bottom: 0;
			border-radius: 20rpx;
			  
			&__item {
				
				&__input {
					margin-top: 59rpx;
					width: 90%;
					height: 77rpx;
					border: 1rpx solid #E6E6E6;
					border-radius: 39rpx;
				  
					&__left-icon {
						width: 10%;
						font-size: 44rpx;
						margin-left: 20rpx;
						color: #838383;
					}
				  
					&__content {
						width: 80%;
						padding-left: 10rpx;
						
						&--verify-code {
							width: 56%;
						}
						
						input {
							font-size: 24rpx;
						  // letter-spacing: 0.1em;
						}
					}
				  
					&__right-icon {
						width: 10%;
						font-size: 44rpx;
						margin-right: 20rpx;
						color: #838383;
					}
					  
					&__right-verify-code {
						width: 34%;
						margin-right: 20rpx;
					}
				}
				
				&__button {
					margin-top: 75rpx;
					margin-bottom: 39rpx;
					width: 90%;
					height: 77rpx;
					text-align: center;
					font-size: 31rpx;
					font-weight: bold;
					line-height: 77rpx;
					letter-spacing: 1em;
					text-indent: 1em;
					border-radius: 39rpx;
					box-shadow: 1rpx 10rpx 24rpx 0rpx rgba(60, 129, 254, 0.35);
				}
				
				&__tips {
					margin: 30rpx 0;
					color: #AAAAAA;
				}
			}
		}
		/* 登录注册信息 end */
		
		/* 登录方式切换 start */
		&__way {
			margin: 0 auto;
			margin-top: 110rpx;
		  
			&__item {
				&--icon {
					width: 85rpx;
					height: 85rpx;
					font-size: 80rpx;
					// border-radius: 100rpx;
					margin-bottom: 18rpx;
					position: relative;
					z-index: 1;
				}
			}
		}
		/* 登录方式切换 end */
		/* 内容 end */
	}
	.t-f {
		position: absolute; 
		bottom: 200rpx; 
		width: 100%;
		text-align: center;
		color: #666;
	}
	.t-e {
		position: absolute;
		bottom: 50rpx; 
		text-align: center;
		width: 100%;
	}
	.t-e image {
		width: 100rpx;
		height: 100rpx;
	}
	
	.sms-dialog {
		height:300rpx;
		width:700rpx;
		border-radius: 30rpx;
		&-box {
			display:flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			margin:0 20rpx;
			border-radius: 20rpx;
			&-input-box {
				display:flex;
				flex-direction:row;
				justify-content: center;
				align-items: center;
				margin-top:50rpx;
				border-radius: 39rpx;
				width:90%;
				height:77rpx;
				border: 1rpx solid #E6E6E6;
			}
			&-btn {
				margin-top:50rpx;
				width:90%;
				height:77rpx;
				text-align:center;
				font-size:31rpx;
				font-weight:bold;
				line-height:77rpx;
				letter-spacing:1em;
				text-indent:1em;
				border-radius:39rpx;
				background-color: #3D7EFF;
				color:white
			}
		}
	}
	
</style>
