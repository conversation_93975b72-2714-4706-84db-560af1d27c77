<template>
	<view>
		<!-- 顶部自定义导航 -->
		<view class="main-top" :style="[{ paddingTop: statusHeight + 'px'}]">
			<!-- 顶部并行任务筛选项 -->
			<view
				style="width:100%;padding-left:10rpx;align-items:center;height:70rpx;display: flex;flex-direction: row;justify-content: flex-start;">
				<uni-icons type="left" size="22" @click="goBack()"></uni-icons>
				<view style="margin:0 10rpx;font-size: 30rpx;display: flex;align-items: center;">
					<text>{{title}}</text>
					<uni-icons v-if="ifCache&&!isCached" style="margin-left: 12rpx;"
						type="star" color="#777" size="20" />
					<uni-icons v-if="ifCache&&isCached" style="margin-left: 12rpx;"
						type="star-filled" color="orange" size="20" />
				</view>
			</view>
			<!-- 并行任务选项栏 -->
			<view v-if="moreTask" :style="showSubPage?'display:flex':'display:none'"
				style="width:750rpx;padding:5rpx; align-items:center;display: flex;flex-direction: row;justify-content:flex-start;flex-wrap: wrap;">
				<view v-for="(item,idx) in moreTask" :key="idx">
					<view @click="switchTask(item)"
						style="background-color: #0fAEFF;color:#fff;border-radius:10rpx;margin:5rpx;padding:0 15rpx;height: 50rpx;font-size: 35rpx;display: flex;align-items:center;">
						{{item.label}}</view>
				</view>
			</view>
			<!-- 子页面 -->
			<view v-if="subPage.length>0" :style="showSubPage?'display:flex':'display:none'" class="subPageContainer">
				<view v-for="(item, index) in subPage" :key="index">
					<button @click="gotoSubPage(item)" type=default class="subPageButton"
						:style="(item.isedit==1?'color:#0fAEFF;':'')+'font-size:30rpx'">{{item.label}}</button>
				</view>
			</view>
		</view>

		<view style="padding-bottom: 140rpx" :style="{marginTop:mainTopHeight+'px'}">
			<view v-for="item,itemIdx in formData" style="flex-direction: column;border-bottom: 1rpx solid #eee;"
				:style="{display: item.type!='fieldset'&&showForm[item.name]||item.type=='fieldset'?'flex':'none'}"
				:key="item.name">
				<!--域标题栏-->
				<view v-if="item.type=='fieldset'" @click="setFieldsetShow(item.name)" class="form-box-row"
					style="justify-content:center;background:linear-gradient(to bottom, #bedeff, #fff);">
					<text style="font-weight: 800;">{{item.label}}</text>
					<text :class="fieldset[item.name].show?'tn-icon-reduce-square':'tn-icon-add-square'"
						style="font-size: 35rpx;right: 20rpx;position: absolute;"></text>
				</view>
				<!-- 图片，GPS图片，拍照，GPS拍照，附件，手写签名-->
				<view
					v-else-if="['SFILE','SGFILE','VIDEO','SVIDEO','SGVIDEO','PFILE','GFILE','FFILE','SIGN','TNSIGN'].indexOf(item.type)>-1"
					class="form-box-col" :class="{disabled:item.disabled}">
					<view class="form-box-title">
						<view style="display: flex;flex-direction: row;align-items: center;"
							:style="'width:'+(item.width||'560')+'rpx;'">
							<uni-icons v-if="item.type=='FFILE'" type="paperclip" size="20"></uni-icons>
							<uni-icons
								v-else-if="['SFILE','SGFILE','PFILE','GFILE','SIGN','TNSIGN'].indexOf(item.type)>-1"
								type="images" size="20"></uni-icons>
							<uni-icons v-else-if="['VIDEO','SVIDEO','SGVIDEO'].indexOf(item.type)>-1" type="videocam"
								size="20"></uni-icons>
							{{item.label}}
							<view v-if="item.required" style='color:red'>*</view>
							<text class="tn-icon-help" v-if="item.help.content&&item.help.type=='tooltip'"
								@click="showHelpTip(item)"></text>
						</view>
						<view v-if="!item.disabled&&cachePics[item.name]&&!offline"
							style="position: absolute;right:200rpx;">
							<button @tap="uploadCache(item)" type=default class="uploadButton">上传缓存图片</button>
						</view>
						<view v-if="!item.disabled" style="position: absolute;right:20rpx;">
							<button v-if="item.type=='SFILE'||item.type=='SGFILE'" @tap="takePhoto(item)" type=default
								class="uploadButton">拍照</button><!-- 拍照和定位拍照 -->
							<button v-else-if="item.type=='SVIDEO'||item.type=='SGVIDEO'" @tap="takeVideo(item)"
								type=default class="uploadButton">摄像</button><!-- 摄像和定位摄像 -->
							<button v-else-if="item.type=='FFILE'" @tap="chooseFile(item)" type=default
								class="uploadButton">上传</button><!-- 普通附件 -->
							<button v-else-if="item.type=='VIDEO'||item.type=='GVIDEO'"
								@tap="chooseVideo(item, ['camera','album'])" type=default
								class="uploadButton">上传</button><!-- 视频和GPS视频 -->
							<button v-else-if="item.type=='PFILE'||item.type=='GFILE'"
								@tap="chooseImage(item, ['camera','album'])" type=default
								class="uploadButton">上传</button><!-- 图片和GPS图片 -->
							<button v-else-if="item.type=='SIGN'" @tap="signature(item)" type=default
								class="uploadButton">签名</button>
							<button v-else-if="item.type=='TNSIGN'" @tap="popTNSign(item)" type=default
								class="uploadButton">签名</button>
						</view>
					</view>
					<!-- 显示图片 -->
					<scroll-view scroll-x="true">
						<view style="display: flex;flex-direction: row;margin-top:10rpx;">
							<view v-for="(pic,index) in item.data" style="position: relative;" :key="pic[0]">
								<image v-if="pic[2]=='pic'" :src="pic[3]" @tap="preViewImg(pic[3])">
									<!-- 正中间显示图片标识 -->
									<view v-if="pic[4]" class="attach-flag">
										{{pic[4]}}
									</view>
								</image>
								<video v-else-if="pic[2]=='video'" :src="pic[3]" @tap="preViewMedia(pic[3])">
									<view v-if="pic[4]" class="attach-flag">
										{{pic[4]}}
									</view>
								</video>
								<uni-icons v-if="!item.disabled" type="close" color="#fff" size="20" class="attach-del"
									@click.stop.native="deleteFile(item.name+'^'+pic[0])">
								</uni-icons>
							</view>
							<view v-for="(pic,index) in cachePics[item.name]" style="position: relative;"
								:key="pic['filePath']">
								<image style="opacity: 0.4;filter: alpha(opacity=40);" :src="pic.filePath"
									@click="preViewImg(pic.filePath)">
									<view @click="preViewImg(pic.filePath)" class="attach-failed">
										未上传成功
									</view>
								</image>
								<uni-icons v-if="!item.disabled" type="close" color="#fff" size="20" class="attach-del"
									@click.stop.native="deleteCachePic(item.name, pic.filePath)">
								</uni-icons>
							</view>
						</view>
					</scroll-view>
					<!-- 附件列表 -->
					<view class="attachfile" v-for="file in item.data" :key='file[0]' v-if="file[2]=='file'"
						@click="download(file)">
						<text>{{ file[1] }}</text>
						<uni-icons v-if="!item.disabled" type="close" color="#fff" size="20"
							style="background:red;margin-left:10rpx;"
							@click.stop.native="deleteFile(item.name+'^'+file[0])">
						</uni-icons>
					</view>
				</view>
				<!--帮助,纯模版,富文本框-->
				<scroll-view scroll-x=true v-else-if="item.type=='template'" style="white-space:nowrap;padding:unset"
					class="form-box-row">
					<rich-text :nodes="item.value" style="padding: 10rpx 0;white-space: pre-wrap;word-break: break-all;"></rich-text>
				</scroll-view>
				<!--单行和多行输入框,高度自动适应&&item.rows-->
				<view v-else-if="item.type=='input'&&!item.popid" v-show="item.type!='hidden'"
					:class="layout=='top'||item.height?'form-box-col':'form-box-row'">
					<view class="form-box-title" :class="{disabled:item.disabled}"
						:style="layout=='left'&&!item.height?'width:'+(item.width||titleWidth)+'rpx;':''">
						<view>{{item.label}}</view>
						<view v-if="item.required" style='color:red;'>*</view>
						<text class="tn-icon-help" v-if="item.help.content&&item.help.type=='tooltip'"
							@click="showHelpTip(item)"></text>
					</view>
					<view
						:class="layout=='top'||layout=='left'||item.height?'form-box-content-left':'form-box-content'">
						<uni-icons v-if="item.call&&item.value" type="phone-filled" size="25" color="orange"
							@click.native.stop="callPhone(item.value)"></uni-icons>
						<text v-if="item.disabled" user-select="true" class="disabled">
							{{item.value}}
						</text>
						<uni-easyinput v-else-if="item.height" v-model="item.value" type="textarea" autoHeight
							:maxlength="item.length"
							:styles="easyinputStyles"
							:inputBorder="false" 
							:placeholderStyle="placeholderStyle"
							:placeholder="item.help.type=='placeholder'&&item.help.content?item.help.content:'请输入'"
							@change="checkRef(item)">
						</uni-easyinput>
						<uni-easyinput v-else v-model="item.value" 
							:textAlign="layout=='top'||layout=='left'?'left':'right'"
							:styles="easyinputStyles" 
							:inputBorder="false" 
							:placeholderStyle="placeholderStyle"
							:placeholder="item.help.type=='placeholder'&&item.help.content?item.help.content:'请输入'"
							@change="checkRef(item)">
						</uni-easyinput>
					</view>
				</view>
				<!--弹窗选择框-->
				<view v-else-if="item.popid" :class="layout=='top'||item.height?'form-box-col':'form-box-row'"
					@click="popList(item)">
					<view class="form-box-title" :class="{disabled:item.disabled}"
						:style="layout=='left'?'width:'+(item.width||titleWidth)+'rpx;':''">
						<view>{{item.label}}</view>
						<view v-if="item.required" style='color:red;'>*</view>
						<text class="tn-icon-help" v-if="item.help.content&&item.help.type=='tooltip'"
							@click="showHelpTip(item)"></text>
					</view>
					<view :style="layout=='top'||layout=='left'||item.height?'justify-content:space-between;':''"
						class="form-box-content">
						<uni-icons v-if="item.call&&item.value" type="phone-filled" size="25" color="orange"
							@click.native.stop="callPhone(item.value)"></uni-icons>
						<text v-if="item.disabled||item.value" :class="{disabled:item.disabled}"
							user-select="true">{{item.value}}</text><!-- 这里如果定义了字典表则值为value显示为showValue -->
						<text v-else
							style="color:#aaa;">{{item.help.type=='placeholder'&&item.help.content?item.help.content:'请选择'}}</text>
						<view v-if="!item.disabled&&item.value" style="padding-right:10rpx">
							<uni-icons color="#999" size="24" type="clear" @click="clearDate(item)" />
						</view>
					</view>
				</view>
				<!--下拉框，做出右侧抽屉方式-->
				<view v-else-if="item.type=='select'||item.type=='multiselect'"
					:class="layout=='top'||item.height?'form-box-col':'form-box-row'" @click="showSelectDraw(item)">
					<view class="form-box-title" :class="{disabled:item.disabled}"
						:style="layout=='left'?'width:'+(item.width||titleWidth)+'rpx;':''">
						<view>{{item.label}}</view>
						<view v-if="item.required" style='color:red'>*</view>
						<text class="tn-icon-help" v-if="item.help.content&&item.help.type=='tooltip'"
							@click="showHelpTip(item)"></text>
					</view>
					<view :style="layout=='top'||layout=='left'||item.height?'justify-content:space-between;':''"
						class="form-box-content">
						<uni-icons v-if="item.call&&item.text" type="phone-filled" size="25" color="orange"
							@click.native.stop="callPhone(item.text)"></uni-icons>
						<text v-if="item.text" user-select="true" :class="{disabled:item.disabled}">{{item.text}}</text>
						<text v-if="!item.disabled&&!item.text"
							style="color:#aaa;">{{item.help.type=='placeholder'&&item.help.content?item.help.content:'请选择'}}</text>
						<uni-icons v-if="!item.disabled" type="right" size="18" color="#999"></uni-icons>
					</view>
				</view>
				<!--checkbox勾选框-->
				<view v-else-if="item.type=='checkbox'"
					:class="layout=='top'||item.height?'form-box-col':'form-box-row'">
					<view class="form-box-title" :class="{disabled:item.disabled}"
						:style="layout=='left'?'width:'+(item.width||titleWidth)+'rpx;':''">
						<view>{{item.label}}</view>
						<view v-if="item.required" style='color:red;'>*</view>
						<text class="tn-icon-help" v-if="item.help.content&&item.help.type=='tooltip'"
							@click="showHelpTip(item)"></text>
					</view>
					<checkbox-group @change="checkboxChange($event,item)" class="form-box-content"
						:style="layout=='top'||layout=='left'||item.height?'justify-content:flex-start;margin:10rpx 0;':''">
						<view v-for="(opt,index) in item.options" v-if="opt.value" :key="index"
							style="text-align:right">
							{{opt.text}}
							<checkbox :value="opt.value" :disabled="item.disabled" :checked="opt.selected"
								style="transform:scale(0.7)" />
						</view>
					</checkbox-group>
				</view>
				<!--radio单选-->
				<view v-else-if="item.type=='radio'" :class="layout=='top'||item.height?'form-box-col':'form-box-row'">
					<view class="form-box-title" :class="{disabled:item.disabled}"
						:style="layout=='left'?'width:'+(item.width||titleWidth)+'rpx;':''">
						<view>{{item.label}}</view>
						<view v-if="item.required" style='color:red;'>*</view>
						<text class="tn-icon-help" v-if="item.help.content&&item.help.type=='tooltip'"
							@click="showHelpTip(item)"></text>
					</view>
					<radio-group @change="radioChange($event,item)" class="form-box-content"
						:style="layout=='top'||layout=='left'||item.height?'justify-content:flex-start;margin:10rpx 0;':''">
						<view v-for="(opt,index) in item.options" v-if="opt.value" :key="index"
							style="text-align:right">
							<label class="radio">
								&nbsp;&nbsp;{{opt.text}}
								<radio :value="opt.value" :disabled="item.disabled" :checked="opt.selected"
									style="transform:scale(0.7)" />
							</label>
						</view>
					</radio-group>
				</view>
				<!--计数器始终靠右显示-->
				<view v-else-if="item.type=='counter'" class="form-box-row">
					<view class="form-box-title" :class="{disabled:item.disabled}"
						:style="layout=='left'?'width:'+(item.width||titleWidth)+'rpx;':''">
						<view>{{item.label}}</view>
						<view v-if="item.required" style='color:red'>*</view>
						<text class="tn-icon-help" v-if="item.help.content&&item.help.type=='tooltip'"
							@click="showHelpTip(item)"></text>
					</view>
					<view class="form-box-content">
						<uni-number-box v-model="item.value" :min="Number.parseFloat(item.min)"
							:max="Number.parseFloat(item.max)" :step="Number.parseFloat(item.step)"
							:disabled="item.disabled" />
					</view>
				</view>
				<!--评分始终靠右显示-->
				<view v-else-if="item.type=='rate'" class="form-box-row">
					<view class="form-box-title" :class="{disabled:item.disabled}"
						:style="layout=='left'?'width:'+(item.width||titleWidth)+'rpx;':''">
						<view>{{item.label}}</view>
						<view v-if="item.required" style='color:red'>*</view>
						<text class="tn-icon-help" v-if="item.help.content&&item.help.type=='tooltip'"
							@click="showHelpTip(item)"></text>
					</view>
					<view class="form-box-content">
						<uni-rate v-model="item.value" :max="item.max" :disabled="item.disabled" />
					</view>
				</view>
				<!--开关始终靠右显示-->
				<view v-else-if="item.type=='switch'" class="form-box-row">
					<view class="form-box-title" :class="{disabled:item.disabled}"
						:style="layout=='left'?'width:'+(item.width||titleWidth)+'rpx;':''">
						<view>{{item.label}}</view>
						<view v-if="item.required" style='color:red'>*</view>
						<text class="tn-icon-help" v-if="item.help.content&&item.help.type=='tooltip'"
							@click="showHelpTip(item)"></text>
					</view>
					<view class="form-box-content">
						<switch :checked="item.value=='1'" v-model="item.value" :disabled="item.disabled"
							style="transform:scale(0.7);margin-right:-20rpx" @change="switchChange($event,item)" />
					</view>
				</view>
				<!-- 日期 -->
				<view v-else-if="item.type=='date'||item.type=='time'"
					:class="layout=='top'||item.height?'form-box-col':'form-box-row'" @click="onShowDatePicker(item)">
					<view class="form-box-title" :class="{disabled:item.disabled}"
						:style="layout=='left'?'width:'+(item.width||titleWidth)+'rpx;':''">
						<view>{{item.label}}</view>
						<view v-if="item.required" style='color:red'>*</view>
						<text class="tn-icon-help" v-if="item.help.content&&item.help.type=='tooltip'"
							@click="showHelpTip(item)"></text>
					</view>
					<view class="form-box-content"
						:style="layout=='top'||layout=='left'||item.height?'justify-content:space-between;':''">
						<text v-if="item.disabled||item.value" :class="{disabled:item.disabled}">{{item.value}}</text>
						<text v-else
							style="color:#aaa">{{item.help.type=='placeholder'&&item.help.content?item.help.content:'请选择'}}</text>
						<uni-icons v-if="!item.disabled&&item.value" color="#999" size="25" type="clear"
							@click="clearDate(item)" />
					</view>
				</view>
				<!-- 展示地图 天地图定位还有问题-->
				<view v-else-if="item.type=='GPS'">
					<!-- #ifndef MP-WEIXIN -->
					<tdtMap :lat="latitude" :lng="longitude" :height="'500rpx'" :onlyShow="true"></tdtMap>
					<!-- #endif -->
					<!-- #ifdef MP-WEIXIN -->
					<map style="width:100%;height:500rpx" :latitude="latitude" :longitude="longitude"
						:markers="markers"></map>
					<!-- #endif -->
				</view>
				<view v-else-if="item.type=='DOTMAP'">
					<view style="font-size: 30rpx;background-color: #fff;padding: 20rpx;">
						{{item.label}}
					</view>
					<!-- #ifndef MP-WEIXIN -->
					<tdtMap :lat="item.value.split(',')[1]" :lng="item.value.split(',')[0]" :height="'500rpx'"
						:onlyShow="true"></tdtMap>
					<!-- #endif -->
					<!-- #ifdef MP-WEIXIN -->
					<map :id="item.name" :ref="item.name" style="width:100%;height:500rpx"
						:latitude="parseFloat(item.value.split(',')[1])||latitude"
						:longitude="parseFloat(item.value.split(',')[0])||longitude"
						:markers="item.value.split(',')[1]?[{id: 1,latitude: item.value.split(',')[1],longitude: item.value.split(',')[0],width: 20,height: 30}]:[]"
						@click="clickMap(item,$event)"></map>
					<!-- #endif -->
				</view>
				<view v-else-if="item.type=='FORMTAB'||item.type=='ZC'">
					<listVue :ref="item['name']" @saveToCache="saveFormToCache" :topButtonHeight="topButtonHeight"
						:listProp="item" :fromCache="fromCache" :editData="{edit_id:id,edit_rid:rid}" />
				</view>
				<view v-if="item.help&&item.help.type=='bottom'&&item.help.content"
					style="font-size: 24rpx;color:grey;padding: 0 20rpx 20rpx;background: #fff;">
					{{item.help.content}}
				</view>
			</view>
		</view>
		<!-- 下拉框做成右边的抽屉 -->
		<uni-drawer ref="selectDrawer" mode="right" @close="closeDrawer">
			<scroll-view scroll-y="true" style="height:calc(100vh - 90rpx); padding-left: 20rpx;">
				<!--#ifdef H5-->
				<view style="position: fixed;top:0;padding:40rpx 0 10rpx 0;width:100%;background-color: #fff;z-index:9999">
				<!--#endif-->
				<!--#ifndef H5-->
				<view
					style="position: fixed;top:0;padding:100rpx 0 10rpx 0;width:100%;background-color: #fff;z-index:9999">
					<!--#endif-->
					<view style="padding:20rpx;font-weight: 700;">选项栏，右滑关闭</view>
					<view v-if="isAuto" style="width:100%;display: flex;flex-direction: row;">
						<input type="text" @input="getOption" class="option-filter" placeholder="输入后自动获取选项">
						<uni-icons v-if="showClear" type="clear" style="position:absolute;right:50rpx;z-index:99"
							size="25" color="#999" @click.native.stop="onClearSearchInput"></uni-icons>
					</view>
					<view v-else style="width:100%;display: flex;flex-direction: row;">
						<input type="text" @input="optionFilter(optionInput)" v-model="optionInput"
							class="option-filter" placeholder="输入关键字查找选项">
						<uni-icons v-if="showClear" type="clear" style="position:absolute;right:50rpx;z-index:99"
							size="25" color="#999" @click="onClearSearchInput"></uni-icons>
					</view>
				</view>
				<!--#ifdef H5-->
				<!--H5下顶部不用预留高度240改成180,但湖北H5环境是小程序，会遮挡，需要多留60的高，仍然需要改为240-->
				<view class="optionContainer" style="margin-top:240rpx">
				<!--#endif-->
				<!--#ifndef H5-->
				<view class="optionContainer" style="margin-top:240rpx">
					<!--#endif-->
					<view v-for="(item, index) in optionData" :key="index" v-if="item.value&&!item.hide">
						<button :style="{width:optionWidth+'rpx'}" @tap="clickOption(item)"
							:class="{'optionBtn':!item.selected,'optionSelect':item.selected}">
							{{item.text}}
						</button>
					</view>
				</view>
			</scroll-view>
			<view class="btnContainer" style="background-color: #fff;border-bottom-left-radius: 25rpx;">
				<button class="selectBtn" @click="clearOption" type="default"
					style="background-color: #3688ff;color: #fff;">清除</button>
				<button class="selectBtn" v-if="isMultiSelect" @click="selectAll" type="default"
					style="background-color: darkorange;color: #fff;">全选</button>
				<button class="selectBtn" @click="setSelectValue" type="default"
					style="background-color: #67C23A;color: #fff;">确定</button>
			</view>
		</uni-drawer>
		<!-- 日期插件 -->
		<mx-date-picker ref="dateTimePicker" :show="showDatePicker" format="yyyy-mm-dd hh:ii:ss" :type="dateType"
			:value="dateValue" :show-tips="true" :show-seconds="true" @confirm="onDateSelected"
			@cancel="onDateCancel" />
		<!-- 保存，提交，退回等按钮 -->
		<view v-if="buttons.length>0" class="btnContainer">
			<view v-for="(item,index) in buttons" :key="index" v-if="index<4">
				<button v-if="index<3" @click="btnSubmit(item.btnAction)" class="searchBtn" type="default">
					{{item.btnLabel}}
				</button>
				<image v-if="index==3" @click="showMoreButton" src="../static/img/more.png"
					style="height: 60rpx;width:60rpx;margin-left: 0;">
					<!-- <uni-icons v-if="index==3" @click="showMoreButton" type="bars" color="#409EFF" size="22" style="margin-right: 15rpx;" /> -->
			</view>
		</view>
		<!-- 中间的弹窗提示框,对比showModal方便关闭，例如打开GPS搜星成功后可以关闭提示 -->
		<uni-popup ref="showtip" type="center" :mask-click="true"><!-- 蒙版点击是否关闭弹窗 -->
			<view class="uni-tip">
				<text class="uni-tip-title">{{tipTitle}}</text>
				<text class="uni-tip-content">{{ tipContent }}</text>
				<view class="uni-tip-group-button">
					<text class="uni-tip-button" @click="closeTip()">确定</text>
				</view>
			</view>
		</uni-popup>
		<!-- 底部更多按钮弹窗 -->
		<uni-popup ref="moreButton" type="bottom" :custom="true">
			<view class="more-button-box">
				<button v-for="(item, index) in buttons" v-if="index>2" :key="index" @click="btnSubmit(item.btnAction)"
					class="searchBtn" type="default" style="margin:10rpx 0">{{item.btnLabel}}</button>
			</view>
			<view class="hide-more-button" @click="hideMoreButton">取消</view>
		</uni-popup>
		<!-- 签名弹窗 -->
		<uni-popup ref="signPopup" @close="closeSignPopup" type="bottom">
			<view style="height: 600rpx;background-color: #fff;width:750rpx;">
				<htz-signature @submit="submitSign" @fail="signFail" cid="htzsignature"></htz-signature>
			</view>
		</uni-popup>
		<baTreePicker ref="treePicker" :multiple='treeMulti' @select-change="treeSelectChange"
			@item-switch="treeSwtichChange" :title="treeTitle" :localdata="treeData" valueKey="id" textKey="value"
			@item-select="treeItemSelect" childrenKey="children" />
		<!-- 图鸟全屏签名弹窗 -->
		<tn-sign-board :show="showTNSign" :customBarHeight="statusHeight+38" :signSelectColor="signSelectColor"
			:rotate="true" @save="saveTNSign" @closed="closeTNSign"></tn-sign-board>
	</view>
</template>

<script>
	var khkv = require('../common/util.js').khkv
	var getPopRef = require('../common/util.js').getPopRef
	var checkOpenGPSService = require('../common/util.js').checkOpenGPSService
	// #ifndef MP-WEIXIN
	import tdtMap from '@/components/tdt-map/tdt-map.vue'
	// #endif
	const app = getApp()
	//import amap from '../common/amap-wx.js'
	import htzSignature from '@/components/htz-signature/htz-signature.vue'
	import MxDatePicker from '@/components/mx-datepicker/mx-datepicker.vue'
	import baTreePicker from "@/components/ba-tree-picker/ba-tree-picker.vue"
	import listVue from './list.vue'
	import {
		mapState,
		mapMutations
	} from 'vuex'
	export default {
		components: {
			// #ifndef MP-WEIXIN
			tdtMap,
			// #endif
			MxDatePicker,
			htzSignature,
			listVue,
			baTreePicker
		},
		created() {
			// #ifdef MP-WEIXIN
			this.statusHeight = uni.getMenuButtonBoundingClientRect().top
			// #endif
			// #ifdef APP-PLUS
			this.statusHeight = 40
			// #endif
		},
		data: function() {
			return {
				signSelectColor: ['#080808', '#E83A30'], //图鸟签名可选颜色
				showTNSign: false,
				statusHeight: 0,
				hasMoreTask: false,
				showMoreTask: false,
				moreTask: [],
				GPS: false,
				userId: '', //当前用户id
				layout: '', //表单标题和内容的布局，分top上下，left内容左对齐，right内容右对齐
				id: '',
				label: '', //模块名称
				rid: '',
				name: '', //记录名称
				title: '', //顶部显示的节点环节名称
				offline: '', //（离线）,仅用于导航栏显示
				realId: '',
				realRid: '',
				edit_taskid: '',
				edit_rid: '',
				edit_nodeid: '',
				edit_key: '',
				edit_master: '',
				treeTitle: '',
				treeMulti: false,
				treeData: [],
				thid: '',
				isTask: '',
				opServer: '',
				editUpdateURL: 'wxSave.php',
				editURL: 'wxEdit.php',
				formData: [],
				developid: '',
				easyinputStyles: {
					color: "#000",
					borderColor: "#fff"
				},
				placeholderStyle: "color:#aaa;font-size:27rpx",
				optionData: [],
				optionWidth: 185,
				isMultiSelect: false,
				currSelect: {},
				showClear: false,
				showSubPage: true,
				optionInput: '',
				buttons: [],
				deleteRowId: '',
				deleteCol: '',
				signatureUrl: '',
				signatureCol: '',
				width: '',
				height: '',
				titleWidth: 200, //默认标题宽度，从服务端获取
				subPage: [],
				isOpenFlow: false,
				showDatePicker: false,
				dateType: 'date',
				dateValue: '',
				uploadRequried: '',
				clear: false,
				isAuto: true,
				btnInterval: '',
				gpsInterval: '',
				gpsRequried: false,
				longitude: '',
				latitude: '',
				currBid: '',
				currBtn: [],
				bidTimeout: '',
				tipTitle: '',
				tipContent: '',
				//amapPlugin: null,
				//amapKey: '255f0968e37c9a451d2fcb4e60b3b616',
				addressOK: false, //是否通过天地图逆地理解析获取到了地址
				imgDemoURL: [],
				fromCache: false,
				cachePics: {}, //未上传成功的图片
				fieldset: {},
				topButtonHeight: 0,
				mainTopHeight: 0,
				markers: [{
					id: 1,
					longitude: '',
					latitude: '',
					iconPath: '../static/img/mark_r_64.png',
					width: 50,
					height: 50,
					anchor: {x: 0.5, y: 0.75}
				}],
				showForm: {},
				startSendCachePic: true,
				ifCache: false,
				isCached: false
			}
		},
		computed: {
			...mapState(['bulletinCount', 'userName'])
		},
		watch: {
			formData: {
				handler(val) {
					if (this.fromCache) {
						let cacheLists = uni.getStorageSync('cacheLists')
						cacheLists[this.userId][this.id][this.rid].formData.datas = this.formData
						uni.setStorageSync('cacheLists', cacheLists)
					}
				},
				deep: true
			}
		},
		onLoad(e) {
			this.opServer = uni.getStorageSync('currServerUrl')
			var that = this
			this.ifCache = e.ifCache === 'true'
			this.isCached = e.isCached === 'true'
			if (!e.fromCache && getApp().globalData.cacheData) {
				this.cacheData = getApp().globalData.cacheData
				getApp().globalData.cacheData = ''
			}
			if (getApp().globalData.offline) {
				this.userId = uni.getStorageSync('userId')
			} else {
				this.userId = getApp().globalData.userId
			}
			this.id = e.id
			// this.amapPlugin = new amap.AMapWX({
			// 	key: this.amapKey
			// });
			if (e.rid) {
				this.rid = e.rid
			}
			if (e.msgid) {
				this.msgid = e.msgid
			}
			if (e.master) {
				this.edit_master = e.master
			}
			if (e.editURL) {
				this.editURL = e.editURL
			}
			if (e.fromCache) { //表示从缓存读取的表单数据
				this.fromCache = true
			}
			this.initEdit(()=>{
				if(e.param){//表单弹窗id进入新表单时需要从原表单带值
					const obj = JSON.parse(decodeURIComponent(e.param))
					for(let colname in obj){
						for (let k = 0; k < that.formData.length; k++) {
							if (colname == that.formData[k].name) {
								that.$set(that.formData[k], 'value', obj[colname])
								that.$set(that.formData[k], 'text', obj[colname])
								that.$set(that.formData[k], 'selected', 1)
								if(that.formData[k]['type']=='select'&&that.formData[k]['options']){
									this.$nextTick(()=>{
										if (that.formData[k].childcols) {
											that.changeChildOptions(that.formData[k].childcols)
										}
									})
									for(let option of that.formData[k]['options']){
										if(option['value']==obj[colname]){
											that.$set(option,'selected',true)
											that.$set(that.formData[k], 'text', option['text'])
											break
										}
									}
								}
								if(that.formData[k]['type']=='multiselect'&&that.formData[k]['options']){
									let multiText = []
									for(let option of that.formData[k]['options']){
										for(let key of obj[colname].split(',')){
											if(option['value']==key){
												that.$set(option,'selected',true)
												multiText.push(option['text']||key)
												break
											}
										}
									}
									that.$set(that.formData[k], 'text', multiText.join(','))
								}
							}
						}
					}
				}
			})
			uni.$on('initEdit', function(e) {
				this.id = e.id
				this.rid = e.rid
				that.initEdit()
			})
			uni.$on('setEditOnline', function(e) { //从离线转为在线状态
				uni.setNavigationBarTitle({
					title: that.title
				})
				that.offline = '' //判断是否显示【上传缓存图片】按钮
			})
			uni.$on('bringPopValue', function(e) { //列表弹窗带回值的处理
				//console.log(e) //传递的格式["col1,col2","val1|val2"],新版格式["col1,$col1","val1|val2"]
				//console.log(that.formData)
				let col = e[0].split(',')
				let val = e[1].split('|')
				for (var i = 0; i < col.length; i++) {
					for (let k = 0; k < that.formData.length; k++) {
						if (col[i] == ("^" + that.formData[k].name) || (col[i] == that.formData[k].name && col.indexOf('$' + col[i])>-1)) {
							that.$set(that.formData[k], 'saveValue', val[i])
						} else if(col[i] == that.formData[k].name || col[i] == ("$" + that.formData[k].name)) {
							that.$set(that.formData[k], 'value', val[i])
						}
					}
				}
			})
		},
		methods: {
			...mapMutations(['setKV']),
			goBack: function() {
				uni.navigateBack()
			},
			getTopButtonsHeight() {
				var query = uni.createSelectorQuery().in(this);
				// 选择类名为.some-class的元素
				query.select('.subPageContainer').boundingClientRect(data => {
					if (data) {
						this.topButtonHeight = data.height;
					}
				}).exec();
				query.select('.main-top').boundingClientRect(data => {
					if (data) {
						this.mainTopHeight = data.height;
					}
				}).exec();
			},
			switchTask: function(e) {
				this.showMoreTask = false
				let that = this
				let url = this.editURL + '?id=myjob&rid=' + e.id
				this.xAjax({ //非离线模式都从后台获取
					url: url
				}, function(res) {
					that.loadRes(res)
				})
			},
			initEdit: function(loaded) {
				if (this.fromCache || getApp().globalData.offline) { //只要是从离线列表过来或离线模式下从缓存读取
					try {
						var cacheLists = uni.getStorageSync('cacheLists')
						uni.stopPullDownRefresh()
						this.loadRes(cacheLists[this.userId][this.id][this.rid].formData)
					} catch {}
					return
				} 
				if (getApp().globalData.formData) {//列表传过来的
					this.loadRes(getApp().globalData.formData)
					if(getApp().globalData.formData.alert){//表单读后的提示语
						uni.showModal({
							content: getApp().globalData.formData.alert
						})
					}
					getApp().globalData.formData = null
					return
				}
				let that = this
				let url = this.editURL + '?id=' + this.id + '&rid=' + this.rid
				if (this.msgid) {
					url += '&msgid=' + this.msgid
				}
				if (this.edit_master) {
					url += '&master=' + this.edit_master
				}
				uni.setNavigationBarTitle({ //标题要先清空，避免获取服务器数据失败而显示错误信息
					title: '加载中...'
				})
				this.xAjax({ //非离线模式都从后台获取
					url: url
				}, function(res) {
					that.loadRes(res)
					if(loaded){
						loaded()
					}
				})
			},
			loadRes(res) {
				var that = this
				this.title = res.title
				if (getApp().globalData.offline) {
					this.offline = "（离线）"
				}
				this.formRes = res
				this.title = res.title + this.offline
				uni.setNavigationBarTitle({
					title: this.title + this.offline
				})
				that.currSelect = {}
				if (res.editUpdateURL) {
					that.editUpdateURL = res.editUpdateURL
				}
				if (res.moreTask) {
					that.hasMoreTask = true
					that.moreTask = res.moreTask
				}
				that.layout = res.layout
				that.label = res.label
				that.name = res.name
				that.realId = res.realid
				that.realRid = res.realrid
				that.isTask = res.istask
				that.edit_taskid = res.edit_taskid
				that.edit_rid = res.edit_rid
				that.edit_nodeid = res.edit_nodeid
				that.thid = res.thid
				that.edit_key = res.edit_key
				that.uploadCols = res.uploadCols
				that.uploadKey = res.uploadKey
				that.titleWidth = res.titleWidth
				that.formData = res.datas
				that.developid = res.developid
				that.subPage = res.tabs
				that.uploadRequried = res.uploadRequried
				that.edit_master = res.edit_master
				that.getFieldset()
				that.getCachePic()
				that.$nextTick(() => {
					that.getTopButtonsHeight()
				})
				if (res.openflow == "1") {
					that.isOpenFlow = true
				}
				that.buttons = res.buttons
				//tab或zc子页面中的弹窗需要传递form中的字段值作为过滤条件
				uni.$on('khkv' + that.realId + that.realRid, function(col) {
					let KV = khkv(that.formData, col)
					that.setKV(KV)
				})
				for (var i = 0; i < that.formData.length; i++) { //显示照片模板
					if (that.formData[i].type == 'template') {//富文本中要把附件图片的相对路径替换成完整路径
						that.formData[i].value = that.formData[i].value.replace(/src="(?!http)([^"]+)"/gi, (match, p1) => {
							return `src="` + that.opServer + "/" + `${p1}"`
						})
					} else if (that.formData[i].data) {//表示是附件[主键, 文件名称, 文件类型, 文件链接/showImage.php?fileid=主键, 显示在图片上的标识例如审核通过]
						for (let k = 0; k< that.formData[i].data.length; k++) {
							that.formData[i].data[k][3] = that.formData[i].data[k][3].replace(/^((?!http).+)/i, that.opServer + '/$1')
						}
					}
					if (that.formData[i].type == 'SGFILE' || that.formData[i].type == 'SGVIDEO') { //定位拍照和定位摄像的要开启定位
						if (!res.GPS) {
							res.GPS = {}
							res.GPS.track = true
						} else {
							res.GPS.track = true
						}
					}
				}
				if (that.btnInterval) {
					clearInterval(that.btnInterval)
				}
				if (res.GPS) {
					that.GPS = true
					for (let i = 0; i < that.formData.length; i++) { //找到经纬度字段，地图按初始值定位
						if (that.formData[i].name == res.GPS.prefix + 'LONGITUDE') {
							that.longitude = that.formData[i].value
							that.$set(that.markers[0], 'longitude', that.longitude)
						} else if (that.formData[i].name == res.GPS.prefix + 'LATITUDE') {
							that.latitude = that.formData[i].value
							that.$set(that.markers[0], 'latitude', that.latitude)
						}
					}
					if (res.GPS.track) { //编辑有权限才开启定位
						if (res.GPS.required) { //后台配置要求必须开启定位
							that.gpsRequried = true //用于按钮操作时判断是否已定位，否则不允许操作
						}
						getApp().globalData.gpsNeed = 1 //打开app.vue中的GPS定位开关
						var lngOld, latOld
						this.gpsInterval = setInterval(function() {
							that.longitude = getApp().globalData.longitude //这里要直接更新，天地图刷新位置
							that.latitude = getApp().globalData.latitude
							that.accuracy = getApp().globalData.accuracy
							that.$set(that.markers[0], 'longitude', that.longitude)
							that.$set(that.markers[0], 'latitude', that.latitude)
							if (getApp().globalData.locationError) { //没有获取到经纬度且未打开定位的才提示
								that.tipTitle = "定位失败"
								that.tipContent = getApp().globalData.locationError
								that.$refs['showtip'].open()
							} else if (that.tipTitle == '定位失败') { //当前显示的是定位tip才关闭，如果是后台刷新返回的“提示”不关闭报错窗口
								that.$refs['showtip'].close()
							}
							if (that.longitude != lngOld || that.latitude != latOld || !that.addressOK) {
								lngOld = that.longitude
								latOld = that.latitude
								for (let i = 0; i < that.formData.length; i++) {
									if (!that.formData[i].disabled) { //允许编辑的才更新
										if (that.formData[i].name == res.GPS.prefix + 'ACCURACY') { //精度一直会更新
											that.$set(that.formData[i], 'value', that.accuracy)
										}
										if (that.accuracy < res.GPS.accuracy || res.GPS.accuracy ==
											'') { //达到了定义的精度或没定义精度才更新经纬度
											if (that.formData[i].name == res.GPS.prefix + 'LONGITUDE') {
												that.$set(that.formData[i], 'value', that.longitude)
											} else if (that.formData[i].name == res.GPS.prefix + 'LATITUDE') {
												that.$set(that.formData[i], 'value', that.latitude)
											} else if (that.formData[i].name == res.GPS.prefix + 'ADDRESS') {
												that.getAddressName(that.longitude, that.latitude, function(
													address) {
													if (address) {
														that.addressOK = true
														that.$set(that.formData[i], 'value', address)
													}
												})
											}
										}
									}
								}
							}
						}, 2000)
					}
				}
			},
			showMoreButton: function() { //页面底部的显示更多按钮
				if (!this.$refs.moreButton.showPopup) {
					this.$refs.moreButton.open()
				} else {
					this.$refs.moreButton.close()
				}
			},
			hideMoreButton() { //隐藏底部的更多按钮
				this.$refs.moreButton.close()
			},
			checkboxChange: function(e, item) {
				this.$set(item, "value", e.detail.value)
			},
			radioChange: function(e, item) {
				this.$set(item, "value", e.detail.value)
			},
			switchChange: function(e, item) {
				this.$set(item, "value", e.detail.value ? 1 : 0)
			},
			showSelectDraw: function(e) { //选择框的显示
				if (!e.disabled) {
					if (e.combAuto == 1) {
						this.isAuto = true
					} else {
						this.isAuto = false
					}
					this.isMultiSelect = false
					if (e.type == 'multiselect') { //多选每次进入都要与当前选中值保持一致，因为存在虽然选中，但未提交的情况
						this.isMultiSelect = true
						for (let i = 0; i < e.options.length; i++) {
							if (e.value != '' && (',' + e.value + ',').indexOf(',' + e.options[i].value + ',') != -1) {
								this.$set(e.options[i], 'selected', true)
							} else {
								this.$set(e.options[i], 'selected', false)
							}
						}
					}
					this.optionData = e.options
					if (!e.options) this.optionData = []
					this.optionWidth = e.optionWidth || this.optionWidth
					this.currSelect = e
					this.$refs.selectDrawer.open()
				}
			},
			getOption(e) { //动态获取下拉选项
				let that = this
				if (e.detail.value.length > 0) {
					let url = 'wxCombofilter.php?id=' + this.realId + '&rid=' + this.realRid + '&col=' + this.currSelect
						.name + khkv(this.formData, this.currSelect.filter) + '&mask=' + e.detail.value
					this.xAjax({
						url: url
					}, function(res) {
						that.setColOption(that.currSelect, res)
					})
				}
			},
			changeChildOptions: function(e) {
				let childcol = e.split(',')
				for (let i = 0; i < childcol.length; i++) {
					this.setChildOptions(childcol[i])
				}
			},
			setChildOptions: function(col) {
				let that = this
				for (let i = 0; i < this.formData.length; i++) {
					if (this.formData[i].name == col && this.formData[i].hasOwnProperty("options")) {//有选项的下拉才需要加载选项
						if (!this.formData[i].oldValue) { //缓存原有值，当父级恢复原来值时，级联下级都恢复成原来值
							this.formData[i].oldValue = this.formData[i].value
						}
						let url = "wxGetOption.php?id=" + this.realId + "&rid=" + this.realRid + "&col=" + col + khkv(
							this.formData, this.formData[i].filter)
						this.xAjax({
							url: url
						}, function(res) {
							that.setColOption(that.formData[i], res)
						})
						if (this.formData[i].childcols) {
							this.changeChildOptions(this.formData[i].childcols)
						}
						break
					}
				}
			},
			setColOption(el, res) {
				let o = [],
					value = '',
					text = ''
				for (let k = 0; k < res.options.length; k++) {
					o[k] = {
						"value": res.options[k][0],
						"text": res.options[k][1]
					}
					if ((',' + el.oldValue + ',').indexOf(',' + res.options[k][0] + ',') != -1 && res.options[k][0] !=
						'') { //包含在原有值中的需要恢复显示
						value += ',' + res.options[k][0]
						text += ',' + res.options[k][1]
						o[k].selected = true
					}
				}
				if (value == '' && res.auto == 1 && res.options.length > 0) { //无选中值的如果配置了默认选中的要选中第一个
					value += ',' + res.options[0][0]
					text += ',' + res.options[0][1]
				}
				this.$set(el, 'options', o) //更新选项列表
				if (this.isAuto) {
					this.$set(this, 'optionData', o) //combo自动要更新抽屉中的选项显示
				}
				this.$set(el, 'value', value.substr(1)) //更新选中值
				this.$set(el, 'text', text.substr(1)) //更新选中显示
			},
			selectAll: function() { //下拉多选的全选
				let len = this.currSelect.options.length
				for (let i = 0; i < len; i++) { //无论单选多选都清空，单选直接更新表单，多选在提交按钮中更新
					if (this.currSelect.options[i].value != '`' && this.currSelect.options[i].value != '' && this
						.currSelect.options[i].value != '^') {
						this.$set(this.currSelect.options[i], 'selected', true)
					}
				}
			},
			clearOption: function() { //选择抽屉的右下角清除点击动作
				let len = this.currSelect.options.length
				for (let i = 0; i < len; i++) { //无论单选多选都清空，单选直接更新表单，多选在提交按钮中更新
					this.$set(this.currSelect.options[i], 'selected', false)
				}
				if (this.currSelect.type == 'select') { //单选框直接清空表单值并关闭抽屉
					this.$set(this.currSelect, "value", "")
					this.$set(this.currSelect, "text", "")
					if (this.currSelect.childcols) {
						this.changeChildOptions(this.currSelect.childcols)
					}
					this.$refs.selectDrawer.close()
				}
			},
			clickOption(o) { //点击下拉框选项
				if (this.currSelect.type == 'multiselect') { //多选
					this.$set(o, 'selected', !o.selected) //这里只更新选项状态，更新表单在提交和清除按钮中
				} else { //单选
					this.$set(o, 'selected', true)
					this.$set(this.currSelect, 'value', o.value)
					this.$set(this.currSelect, 'text', o.text)
					if (this.currSelect.reload == '1' && this.rid != '') { //表示要重新加载，因为展示内容不同,单选才需要重载,注意0节点无需加载
						this.$refs.selectDrawer.close()
						this.btnSubmit('update')
						this.initEdit()
						return
					}
					for (let i = 0; i < this.currSelect.options.length; i++) {
						if (this.currSelect.options[i].value != o.value) {
							this.$set(this.currSelect.options[i], 'selected', false)
						}
					}
					this.$refs.selectDrawer.close()
					if (this.currSelect.childcols) { //级联下拉框
						this.changeChildOptions(this.currSelect.childcols)
					}
				}
			},
			optionFilter: function(keyword) { //下拉框过多选项时可以通过关键字筛选
				if (keyword) {
					this.showClear = true
				}
				let len = this.currSelect.options.length
				for (let i = 0; i < len; i++) {
					if (this.currSelect.options[i].text.indexOf(keyword) < 0) {
						this.$set(this.currSelect.options[i], "hide", true)
					} else {
						this.$set(this.currSelect.options[i], "hide", false)
					}
				}
			},
			onClearSearchInput: function() { //下拉框关键字清空
				this.showClear = false
				this.optionInput = ''
				this.optionFilter('')
			},
			setSelectValue: function() { //选择抽屉的右下角确认点击动作
				let text = [],
					value = []
				let len = this.currSelect.options.length
				for (let i = 0; i < len; i++) {
					if (this.currSelect.options[i].selected) {
						text.push(this.currSelect.options[i].text)
						value.push(this.currSelect.options[i].value)
					}
				}
				this.$set(this.currSelect, 'text', text.join(','))
				this.$set(this.currSelect, 'value', value.join(','))
				if (this.currSelect.childcols) {
					this.changeChildOptions(this.currSelect.childcols)
				}
				this.$refs.selectDrawer.close()
			},
			closeDrawer: function() { //关闭下拉框弹出的抽屉页面
				this.$refs.selectDrawer.close()
			},
			onShowDatePicker: function(e) { //日期控件设置值
				if (this.clear) { //点击清除会同时触发行点击进入日期设置，这里需要阻止，并且使用click.stop无效
					this.clear = false
					return
				}
				this.currSelect = e
				if (!e.disabled) {
					if (e.type == 'time') {
						this.dateType = 'datetime'
					} else {
						this.dateType = 'date'
					}
					this.dateValue = e.value
					this.showDatePicker = true
				}
			},
			clearDate: function(e) { //日期控件和文本以及弹窗控件清除值
				this.clear = true
				this.$set(e, 'value', '')
				if (e.showValue) {
					this.$set(e, 'showValue', '')
				}
			},
			onDateSelected: function(e) {
				this.$set(this.currSelect, 'value', e.value)
				this.showDatePicker = false
			},
			onDateCancel: function(e) {
				this.showDatePicker = false
			},
			gotoSubPage: function(e) { //链接到子页面
				let url = ''
				if (e.loadURL) { //跳转到H5页面
					if (e.loadURL.indexOf(".vue") > -1) { //小程序本地跳转
						url = e.loadURL.split(".")[0] + '?id=' + e.id + '&label=' + e.label
					} else { //跳转到H5页面
						url = 'link?id=' + e.id + '&label=' + e.label + '&url=' + encodeURIComponent(e.loadURL)
					}
				} else {
					url = 'list?id=' + e.id + '&label=' + e.label
				}
				if (e.dataURL) {
					url += "&dataURL=" + e.dataURL
				}
				uni.navigateTo({
					url: url + '&master=' + e.master
				})
			},
			getBtn(bid) { //通过按钮ID获取按钮
				for (let i = 0; i < this.buttons.length; i++) {
					if (bid == this.buttons[i].btnAction) {
						return this.buttons[i]
					}
				}
			},
			btnSubmit: function(bid) { //点击按钮的响应
				let that = this
				if (this.bidTimeout) { //避免按钮连续点击，3秒后才能重新点击
					return
				} else {
					this.bidTimeout = setTimeout(function() {
						clearTimeout(that.bidTimeout)
						that.bidTimeout = null
					}, 2000)
				}
				this.hideMoreButton()
				this.currBid = bid
				this.currBtn = this.getBtn(bid)
				if (bid.substr(0, 3) == 'qrc') { //生成二维码
					var qrcodeValue;
					let len = this.formData.length
					for (let i = 0; i < len; i++) {
						if (this.formData[i].name == this.currBtn.btnQueryURL) {
							qrcodeValue = this.formData[i].value
						}
					}
					if (!qrcodeValue) {
						uni.showModal({
							title: '页面上没有找到需要生成二维码的内容!'
						})
						return false;
					}
					let url = "link?url=" + encodeURIComponent("/phpqrcode/generateQrcode.php?t=" + Date.now() +
						"&qrContent=" + qrcodeValue)
					uni.navigateTo({
						url: url
					})
					return
				}
				if (['sbmt', 'btns', 'scas'].indexOf(bid.substr(0, 4)) > -1) {
					if (this.gpsRequried && !this.latitude) {
						uni.showModal({
							title: '还未定位，请确认已开启位置服务'
						})
						return
					}
				}
				if (bid == 'new') {
					this.id = this.realId
					this.rid = ''
					this.formData = []
					this.initEdit()
				} else if (bid == 'delete') { //ok
					uni.showModal({
						title: '提示',
						content: '确认要删除记录吗',
						success: function(a) {
							if (a.confirm) {
								let url = 'wxDelete.php?id=' + that.id + '&rid=' + that.realRid
								if (that.edit_master) {
									url += '&master=' + that.edit_master
								}
								that.xAjax({
									url: url
								}, function(res) {
									uni.showModal({
										content: '记录已删除'
									})
								})
							}
						}
					})
				} else if (bid == 'help') {
					uni.navigateTo({
						url: 'link?url=wxHelp.php?id=' + this.realId
					})
				} else if (bid.substr(0, 4) == 'recv' || bid == 'forceAccept') { //接单ok
					let url = 'wxFetchTask.php?id=' + this.realId + '&rid=' + this.realRid + '&taskid=' + this.edit_taskid + '&nodeid=' + this.edit_nodeid + '&KEY=' + this.edit_key
					this.xAjax({
						url: url
					}, function(res) {
						that.initEdit()
					})
				} else if (bid.substr(0, 4) == 'rele') { //释放ok
					let url = 'wxFreeTask.php?taskid=' + this.edit_taskid
					this.xAjax({
						url: url
					}, function(res) {
						that.initEdit()
					})
				} else if (bid == 'trace' || bid == 'untrace') { //追踪ok
					let url = 'wxTraceTask.php'
					let data = {
						rid: this.edit_rid,
						act: bid
					}
					this.xAjax({
						url: url,
						data: data
					}, function(res) { //操作成功后更新按钮为反向操作，不用刷新页面
						for (var i = 0; i < that.buttons.length; i++) {
							if (that.buttons[i].btnAction == bid) {
								if (bid == 'trace') { //原来是追踪的要变成取消
									that.$set(that.buttons[i], 'btnAction', 'untrace')
									that.$set(that.buttons[i], 'btnLabel', '取消追踪')
								} else {
									that.$set(that.buttons[i], 'btnAction', 'trace')
									that.$set(that.buttons[i], 'btnLabel', '追踪')
								}
							}
						}
					})
				} else if (bid.substr(0, 3) == 'app') { //
					let filter = ''
					if (this.currBtn.btnFilter) {
						filter = khkv(this.formData, this.currBtn.btnFilter)
					}
					let url = ''
					if (this.currBtn.btnHref.indexOf(".vue") > -1) {
						let ref = this.currBtn.btnHref.split(".vue")
						url = ref[0] + (ref[1] || "")
					} else {
						url = 'link?id=' + e.id + '&label=' + this.currBtn.btnLabel + '&url=' + encodeURIComponent(this
							.currBtn.btnHref)
					}
					url += '?master=' + this.currBtn.btnMaster + (filter ? "&filter=" + encodeURIComponent(filter) :
						"")
					uni.navigateTo({
						url: url
					})
				} else if (bid.substr(0, 4) == 'pope') {
					let url = 'edit?id=' + this.currBtn.href
					if (this.currBtn.btnMaster) {
						url += '&master=' + this.currBtn.btnMaster
					}
					if (this.currBtn.hrefFilter) {
						let tempdata = this.getFormKV(true)
						const replacedStr = this.currBtn.hrefFilter.replace(/\[(\w+)\]/g, (match, key) => {
							return tempdata.hasOwnProperty(key) ? tempdata[key] : match
						})
						// 解析替换后的字符串为对象
						const result = {};
						replacedStr.split(',').forEach(pair => {
							const [key, value] = pair.split('=').map(s => s.trim())
							result[key] = value
						})
						url += '&param='+ encodeURIComponent(JSON.stringify(result))
					}
					uni.navigateTo({
						url: url
					})
				} else if (bid.substr(0, 3) == 'pop') { //目前主要应用为开发页面的发布、复制等弹出页面--------------------待开发
					let filter = ''
					if (this.currBtn.btnFilter) {
						filter = khkv(this.formData, this.currBtn.btnFilter)
					}
					let url = 'wxList.php?id=' + this.currBtn.btnHref + '&master=' + this.currBtn.btnMaster + filter
					//通过页面ID查定义后分为外部页面和内部列表页面弹窗
					this.xAjax({
						url: url
					}, function(res) {
						if (res.loadURL) {
							let url = 'wxPopRegister.php?id=' + that.realId
							that.xAjax({
								url: url
							}, function(stamp) { //先要登记注册，访问对方系统先验证用户合法性
								uni.navigateTo({
									url: 'link?url=' + encodeURIComponent(res.loadURL) +
										"&stamp=" + stamp.stamp
								})
							})
						} else {
							uni.navigateTo({
								url: 'list?id=' + that.currBtn.btnHref + '&btn=' + that.currBtn
									.btnAction +
									'&popproc=1&btnlabel=' + that.currBtn.btnLabel + '&popfilter=' +
									filter + '&master=' + that.currBtn.btnMaster
							})
						}
					})
				} else if (bid.substr(0, 4) == 'zban') {
					this.getFlow()
				} else if (bid == 'editSave') { //ok
					if (this.isOpenFlow && this.edit_nodeid > 0) { //启用流程的非创建环节做保存不需要校验
						this.sendForm()
					} else {
						if (!this.formValidate()) return //校验没通过的
						this.sendForm()
					}
				} else if (bid.substr(0, 4) == 'sbmt') {//正常流程，提交的confirm是保存后返回，这里不处理
					if (this.currBtn.btnVerify == 1 && !this.formValidate()) return //校验没通过
					this.sendForm()
				}  else if (bid.substr(0, 4) == 'btns') {//提交按钮发起的流程
					if (this.currBtn.btnVerify == 1 && !this.formValidate()) return //校验没通过
					if (this.currBtn.btnConfirm == '') {
						this.sendForm()
					} else {
						uni.showModal({
							content: this.currBtn.btnConfirm,
							success: (a) => {
								if (a.confirm) {
									that.sendForm()
								}
							}
						})
					}
				} else if (bid.substr(0, 4) == 'back' || bid.substr(0, 4) == 'pull' || bid.substr(0, 4) == 'corr') { //corr是退回后处理完成原路返回的按钮
					if (bid.substr(0, 4) == 'back' && this.currBtn.btnVerify == 1 && !this.formValidate()) return //校验没通过
					this.getFlow()
				} else if (bid.substr(0, 4) == 'btnb') {
					if (this.currBtn.btnVerify == 1 && !this.formValidate()) return //校验没通过
					if (this.currBtn.btnConfirm == '') {
						if (this.currBtn.btnCheckProc == '') {
							this.editBtnProc()
						} else {
							this.editBtnValidate()
						}
					} else {
						uni.showModal({
							content: this.currBtn.btnConfirm,
							success: (a) => {
								if (a.confirm) {
									if (that.currBtn.btnCheckProc == null) {
										that.editBtnProc()
									} else {
										that.editBtnValidate()
									}
								}
							}
						})
					}
				} else if (bid == 'develop') {
					uni.navigateTo({
						url: 'edit?id=sysconf&rid=' + this.developid
					})
				} else if (bid.substr(0, 3) == 'sca') { //扫码ok
					// #ifdef MP-WEIXIN||APP-PLUS
					uni.scanCode({
						scanType: ['barCode', 'qrCode', 'datamatrix', 'pdf417'],
						success: (res) => {
							this.dealScanData(res.result)
						}
					})
					// #endif
					// #ifdef H5
					uni.navigateTo({
						url: '/pagesA/scanCodeH5'
					})
					// #endif
				} else {
					uni.showModal({
						content: '没有定义' + that.currBtn.btnAction + '指令'
					})
				}
			},
			dealScanData(res) {
				let that = this
				if (res.indexOf("{") > -1) { //表明是json
					that.scanParse(res)
					return
				}
				if (!that.currBtn.btnHref) {
					uni.showModal({
						content: '扫码结果为:' + res
					})
					return
				}
				let url = that.currBtn.btnHref + '?id=' + that.realId + '&rid=' + that.realRid + '&scanCode=' + res
				if (that.edit_master) {
					url += '&master=' + that.edit_master
				}
				that.scanConfirm(that.currBtn.btnConfirm.replace(/\[\]/, '[' + res + ']'), 3, url)
			},
			//表单校验
			formValidate: function() {
				let errCount = 0
				let isAlert = 0
				let validateError = ""
				let requiredValidate = []
				let integerValidate = []
				let numericValidate = []
				let alphaValidate = []
				let tooLong = []
				let val = ""
				for (let i = 0; i < this.formData.length; i++) {
					if (this.formData[i].name && !this.formData[i].disabled) {
						if (this.formData[i].required && this.formData[i].value == '') {
							errCount++
							requiredValidate.push(this.formData[i].label)
						} else if (this.formData[i].value != '') {
							if (this.formData[i].validate == 'integer') {
								if ((/^\d+$/).test(this.formData[i].value) == false) {
									errCount++
									integerValidate.push(this.formData[i].label)
								}
							} else if (this.formData[i].validate == 'numeric') {
								if ((/^-?\d+\.?\d*$/).test(this.formData[i].value) == false) {
									errCount++
									numericValidate.push(this.formData[i].label)
								}
							} else if (this.formData[i].validate == 'alphaNumeric') {
								if ((/^[0-9a-zA-Z]+$/).test(this.formData[i].value) == false) {
									errCount++
									alphaValidate.push(this.formData[i].label)
								}
							}
							val = this.formData[i].saveValue || this.formData[i].value
							if (this.formData[i].length && val.length > this.formData[i].length) {
								errCount++
								tooLong.push(this.formData[i].label + "长度" + this.formData[i].value.length + "超过" +
									this.formData[i].length + "的最大长度")
							}
						}
						if (errCount == 1 && isAlert == 0) {
							this.$set(this.formData[i], 'focus', true)
							isAlert = 1
						}
					}
				}
				if (errCount > 0) {
					if (requiredValidate.length > 0) {
						validateError += ',' + requiredValidate.join(",") + '是必填项'
					}
					if (integerValidate.length > 0) {
						validateError += ',' + integerValidate.join(",") + '只能填整数'
					}
					if (numericValidate.length > 0) {
						validateError += ',' + numericValidate.join(",") + '只能填数字'
					}
					if (alphaValidate.length > 0) {
						validateError += ',' + alphaValidate.join(",") + '只能填字母和数字'
					}
					if (tooLong.length > 0) {
						validateError += ',' + tooLong.join(",")
					}
					uni.showModal({
						title: errCount + '项填写不规范' + validateError
					})
					return false
				}
				return true
			},
			//获取表单键值对
			getFormKV: function(allFlag) {
				let kv = {}
				let len = this.formData.length
				for (let i = 0; i < len; i++) {
					if ((this.formData[i].name && this.formData[i].type != "template" && this.formData[i].hasOwnProperty('value') && !this.formData[i].disabled)||allFlag) { //hasOwnProperty排除附件字段
						kv[this.formData[i].name] = this.formData[i].saveValue || this.formData[i].value
					}
				}
				kv["id"] = this.realId
				kv["edit_bid"] = this.currBid
				kv["edit_taskid"] = this.edit_taskid
				kv["edit_rid"] = this.edit_rid
				kv["edit_nodeid"] = this.edit_nodeid
				kv["edit_key"] = this.edit_key
				kv["edit_master"] = this.edit_master
				return kv
			},
			//发送表单到后台
			sendForm: function() {
				let that = this
				let postData = this.getFormKV()
				for (let listItem of this.formData) {//保存列表子模块的缓存更新
					if (listItem.type == 'ZC' || listItem.type == 'FORMTAB') {
						this.$refs[listItem['name']][0].saveCacheList()
					}
				}
				this.xAjax({
					url: that.editUpdateURL,
					data: postData,
					hideNetworkErr: true,
					timeout: 10000
				}, function(res) {
					that.response(res)
					if (res.rid) { //是新建，返回了rid
						that.rid = res.rid
						that.edit_rid = res.rid
						that.edit_key = res.key
						if (that.edit_nodeid == 0) {
							that.edit_nodeid = 1;
						}
					}
					if (that.currBid == 'editSave') {
						if (!res.msg && !res.alert && !res.tip) {
							uni.showToast({
								title: '已保存',
								duration: 3000
							})
						}
						if (res.close) { //关闭表单页
							uni.navigateBack()
						} else if (res.refresh) { //刷新表单页
							that.initEdit()
						}
					} else { //提交的流程,要判断按钮是否要校验
						/*************按钮是否做校验需要获取**********************************/
						if (that.currBtn.btnNoVerify != 1 && that.uploadRequried != '') { //校验附件
							let url = 'wxCheckAttach.php?id=' + that.realId + '&rid=' + that.realRid + '&filename=' + that.uploadRequried
							that.xAjax({
								url: url
							}, function() {
								that.flowConfirm(res)
							})
						} else {
							that.flowConfirm(res)
						}
					}
				}, () => {
					if (this.cacheData&&this.cacheData.ifCache) {
						uni.showModal({
							title: '提示',
							content: '保存失败,是否要离线保存?',
							success: (res) => {
								if (res.confirm) {
									this.saveFormToCache()
								}
							}
						})
						return
					}
					if(this.fromCache){
						uni.showModal({
							title: '提示',
							content: '服务器连接失败,已离线保存'
						})
						return
					}
				})
			},
			//后台返回信息的处理
			response: function(data) { //返回值更新到表单中
				if (data.tip) { //中间的弹层显示
					if (data.tipTitle) {
						this.tipTitle = data.tipTitle
					} else {
						this.tipTitle = '提示'
					}
					this.tipContent = data.tip
					this.$refs.showtip.open()
				} else if (data.closeTip) {
					this.$refs.showtip.close()
				}
				if (data.response) {
					var col = {}
					for (var i = 0; i < data.response.length; i++) {
						for (var k = 0; k < this.formData.length; k++) {
							if (data.response[i][0] == this.formData[k].name) {
								this.$set(this.formData[k], 'value', data.response[i][1])
								if (this.formData[k].type == 'select' || this.formData[k].type ==
									'multiselect') { //格式{"response":[["col","value"],["col",[["value","text","selected"],["value","text","selected"]]]]}
									this.$set(this.formData[k], 'options', data.response[i][1][j])
									var text = []
									for (var j = 0; j < data.response[i][1].length; j++) {
										var v = data.response[i][1][j]
										if (v[2] == 1) {
											text.push(v[1])
										}
									}
									this.$set(this.formData[k], 'text', text.join(','))
								}
							}
						}
					}
				}
				if (data.wxJS) {
					eval(data.wxJS)
				}
			},
			getFlow() {
				let that = this
				let data = this.getFormKV()
				let url = 'wxGetFlow.php'
				this.xAjax({
					url: url,
					data: data
				}, function(res) {
					that.flowConfirm(res)
				})
			},
			flowConfirm: function(data) { //提交前的提示
				let that = this
				if (data.confirm) {
					uni.showModal({
						content: data.confirm,
						success: function(a) {
							if (a.confirm) {
								that.flowWindow(data)
							}
						}
					})
				} else {
					that.flowWindow(data)
				}
			},
			flowWindow(data) {
				if (this.currBid.substr(0, 3) == "btn") {
					this.editBtnProc()
				} else {
					getApp().globalData.flowData = data
					uni.navigateTo({
						url: 'flow?id=' + this.realId + '&rid=' + this.realRid + '&title=' + this.title +
							'&thid=' + this.thid + '&taskid=' + this.edit_taskid + '&isTask=' + this.isTask +
							'&btnid=' + this.currBid + '&btnlabel=' + this.currBtn.btnLabel + '&fromCache='+ this.fromCache
					})
				}
			},
			//提交前检查
			editBtnValidate() {
				let that = this
				let url = 'wxEditValidate.php?id=' + this.realId + '&rid=' + this.realRid + '&bid=' + this.currBid
				this.xAjax({
					url: url
				}, function(res) {
					if (res.confirm) {
						uni.showModal({
							content: res.confirm,
							success: (a) => {
								if (a.confirm) {
									that.editBtnProc()
								}
							}
						})
					} else {
						that.editBtnProc()
					}
				})
			},
			//提交按钮响应
			editBtnProc() {
				let that = this
				let data = this.getFormKV()
				let url = 'wxEditProc.php'
				this.xAjax({
					url: url,
					data: data
				}, function(res) {
					that.response(res)
					if (!res.msg && !res.alert && !res.tip) {
						uni.showToast({
							title: '已执行',
							duration: 3000
						})
					}
					//执行按钮后定时查询结果
					
					if (that.currBtn['btnQueryURL'] && !that.btnInterval) {
						if (!that.currBtn['btnInterval']) {
							that.currBtn['btnInterval'] = 3
						}
						if (!that.currBtn['btnMax']) {
							that.currBtn['btnMax'] = 10
						}
						var data = {
							id: that.realId,
							rid: that.realRid,
							bid: that.currBid
						}
						var i = 1;
						that.btnInterval = setInterval(function() {
							let url = that.currBtn['btnQueryURL']
							that.xAjax({
								url: url,
								data: data
							}, function(res2) {
								that.response(res2)
								if (res2.err || res2.end) {
									clearInterval(that.btnInterval)
									that.btnInterval = null
									if (that.tipTitle != '定位失败') { //不是定位错误的tip要关闭
										that.$refs.showtip.close()
									}
								}
							}, 'hideLoading') //刷新后台执行状态时不显示加载中
							i++
							if (i > that.currBtn['btnMax']) {
								clearInterval(that.btnInterval)
								that.btnInterval = null
							}
						}, that.currBtn['btnInterval'] * 1000)
					} else if (that.currBtn.btnEndAction == 2) { //1=关闭，2=刷新
						that.initEdit()
					} else if (that.currBtn.btnEndAction == 1) {
						uni.navigateBack()
					}
					//执行按钮后定时查询结果	
				})
			},
			popList: function(e) {
				if (!e.disabled && !this.clear) {
					if (e.tree) {
						this.treePop(e)
					} else {
						let url = 'list?id=' + e.popid + '&popget=' + e.popget + '&popwrite=' + e.popwrite +
							'&master=' + e.popmaster + '&multi=' + e.multi
						if (e.popfilter) {
							url += getPopRef(this.formData, e.popfilter);
						}
						uni.navigateTo({
							url: url //master传键值对时有=号无法正常传递
						})
					}
				}
				this.clear = false
			},
			treePop: function(e) {
				if (this.treeObj == e) {
					this.$refs.treePicker._show()
					return
				}
				this.$refs.treePicker._reTreeList()
				this.treeMulti = e.multi ? true : false
				this.treeId = e.popid
				this.treeObj = e
				this.xAjax({ //非离线模式都从后台获取
					url: 'webixTreeClassData.php?id=' + e.popid
				}, (res) => {
					this.treeData = []
					for (let node of res.data) {
						if (node.webix_kids) {
							node.children = [{
								id: '',
								value: '加载中'
							}]
						}
						this.treeData.push(node)
					}
					this.$refs.treePicker._show()
				})
			},
			treeSelectChange(values, names) {
				this.treeObj.saveValue = values.join(',')
				this.treeObj.value = names.join(',')
			},
			treeItemSelect(e) {
				let findNode = this.findTreeNode(e.id, this.treeData)
				findNode.checkStatus = e.checkStatus
			},
			treeSwtichChange(e) {
				let findNode = this.findTreeNode(e.id, this.treeData)
				findNode.showChildren = e.isShowChild
				if (e.isShowChild && e.originChildren[0].id == '') {
					this.xAjax({
						url: 'webixTreeClassData.php?id=' + this.treeId + '&parent=' + e.id
					}, (res) => {
						let treeData = []
						for (let node of res.data) {
							if (node.webix_kids) {
								node.children = [{
									id: '',
									value: '加载中'
								}]
							}
							treeData.push(node)
						}
						findNode.children = treeData
					})
				}
			},
			findTreeNode(id, children) {
				for (let node of children) {
					if (node.id == id) {
						return node
					}
					if (node.children) {
						let findChildren = this.findTreeNode(id, node.children)
						if (findChildren) {
							return findChildren
						}
					}
				}
				return null
			},
			closeTip: function() {
				this.$refs.showtip.close()
			},
			signature(e) { //手写签名控件
				this.signatureCol = e
				this.$refs.signPopup.open()
				// #ifdef APP
				this.isPullDown(false)
				// #endif
			},
			isPullDown(isPull) {
				const pages = getCurrentPages(); //获取页面实列
				const page = pages[pages.length - 1]; //获取最上层页面实列
				const currentWebview = page.$getAppWebview(); //获取当前页面对应的Webview实列
				currentWebview.setStyle({
					pullToRefresh: {
						support: isPull,
					}
				});
			},
			submitSign(res) { //签名后提交确认
				try {
					this.signatureUrl = res.tempFilePath
					this.uploadFile(this.signatureCol, [{
						path: this.signatureUrl,
						name: this.signatureUrl
					}])
					this.$refs.signPopup.close()
					// #ifdef APP
					this.isPullDown(true)
					// #endif
				} catch (e) {
					//error
				}
			},
			signFail(e) {
				uni.showModal({
					content: '签名失败:' + e
				})
			},
			closeSignPopup() { //关闭签名弹窗
				this.$refs.signPopup.close()
				// #ifdef APP
				this.isPullDown(true)
				// #endif
			},
			// 图鸟签名
			popTNSign(e) {
				this.signatureCol = e
				this.showTNSign = true
			},
			closeTNSign() {
				this.showTNSign = false
			},
			saveTNSign(res) { //签名后提交确认
				try {
					this.signatureUrl = res
					this.uploadFile(this.signatureCol, [{
						path: this.signatureUrl,
						name: this.signatureUrl
					}]);
					this.showTNSign = false
				} catch (e) {
					// error
				}
			},
			deleteFile(e) {
				let idx = e.split('^')
				this.deleteCol = idx[0]
				this.deleteRowId = idx[1]
				this.deleteAttach()
			},
			deleteAttach() {
				let that = this
				uni.showModal({
					title: '提示',
					content: '删除后将不可恢复，确认要删除吗？',
					success: function(a) {
						if (a.confirm) {
							let url = 'wxDelFile.php?fileid=' + that.deleteRowId + '&id=' + that.realId +
								'&rid=' + that.realRid + '&taskid=' + that.edit_taskid + '&nodeid=' + that
								.edit_nodeid + '&KEY=' + that.edit_key + '&col=' + that.deleteCol +
								'&master=' + that.edit_master
							that.xAjax({
								url: url
							}, function(res) {
								for (var i = 0; i < that.formData.length; i++) {
									if (that.formData[i].name == that.deleteCol) {
										for (var k = 0; k < that.formData[i].data.length; k++) {
											if (that.formData[i].data[k][0] == that.deleteRowId) {
												that.formData[i].data.splice(k, 1)
											}
										}
									}
								}
							})
						}
					}
				})
			},
			chooseFile(col) { //选择附件图片上传
				let that = this
				//#ifdef H5
				uni.chooseFile({
					success: function(res) {
						that.uploadFile(col, res.tempFiles)
					}
				})
				//#endif
				//#ifdef APP-PLUS
				uni.chooseImage({
					success: function(res) {
						that.uploadFile(col, res.tempFiles)
					}
				})
				//#endif
				//#ifdef MP-WEIXIN
				wx.chooseMessageFile({
					success: function(res) {
						that.uploadFile(col, res.tempFiles)
					}
				})
				//#endif
			},
			takeVideo(col) { //拍摄短视频
				// #ifdef MP-WEIXIN||APP-PLUS
				if (this.platform != 'ios' && this.platform != 'android' && this.platform != 'devtools' && this.platform != 'ohos') {
					this.showErr('只能使用手机摄像!')
					return
				}
				//#endif
				if (col.type == 'SGVIDEO') {
					// #ifdef MP-WEIXIN||APP-PLUS
					if (!checkOpenGPSService()) {
						this.showErr('请开启位置服务后再摄像!')
						return
					}
					//#endif
					if (!getApp().globalData.latitude) {
						var that = this
						uni.showModal({
							title: '提示',
							content: '还未定位，要继续摄像吗?',
							success(c) {
								if (c.confirm) {
									that.chooseVideo(col, ['camera'])
								}
							}
						})
					} else {
						this.chooseVideo(col, ['camera'])
					}
				} else {
					this.chooseVideo(col, ['camera'])
				}
			},
			chooseVideo(col, sourceType) {
				let that = this
				uni.chooseVideo({
					sourceType: sourceType,
					compressed: true,
					success: function(res) {
						that.uploadFile(col, [{
							"duration": res.duration,
							"path": res.tempFilePath
						}], 'pic')
					},
					fail: function(err) {
						console.log(err)
					}
				})
			},
			takePhoto(col) { //手机拍照
				// #ifdef MP-WEIXIN||APP-PLUS
				if (this.platform != 'ios' && this.platform != 'android' && this.platform != 'devtools' && this.platform != 'ohos') {//ohos=鸿蒙
					this.showErr('只能使用手机拍照!')
					return
				}
				// #endif
				if (col.type == 'SGFILE') {
					// #ifdef MP-WEIXIN||APP-PLUS
					if (!checkOpenGPSService()) {
						this.showErr('请开启位置服务后再拍照!')
						return
					}
					// #endif
					if (!getApp().globalData.latitude) {
						var that = this
						uni.showModal({
							title: '提示',
							content: '还未定位，要继续拍照吗?',
							success(c) {
								if (c.confirm) {
									that.chooseImage(col, ['camera'])
								}
							}
						})
					} else {
						this.chooseImage(col, ['camera'])
					}
				} else {
					this.chooseImage(col, ['camera'])
				}
			},
			chooseImage(col, sourceType) { //选择图片上传
				let that = this
				uni.chooseImage({
					sourceType: sourceType,
					sizeType: ['compressed'],
					success: function(res) {
						that.uploadFile(col, res.tempFiles, 'pic')
					},
					fail: function(err) {
						console.log(err)
					}
				})
			},
			saveToLocal(url) { //保存图片到手机
				uni.downloadFile({
					url: url,
					success: res => {
						let imgsArray = [];
						imgsArray[0] = res.tempFilePath
						if (res.statusCode === 200) {
							uni.previewImage({
								current: 0,
								urls: imgsArray
							});
						} else {
							uni.showModal({
								content: '下载失败'
							})
						}
					}
				});
			},
			preViewImg(url) { //点击图片后预览图片
				uni.previewImage({
					current: 0,
					urls: [url]
				})
			},
			preViewMedia(url) { //点击视频后预览视频
				var source = [{
					url: url,
					type: "video"
				}]
				wx.previewMedia({
					sources: source,
					current: 0, // 当前显示的资源序号
					url: url // 当前预览资源的url链接
				});
			},
			uploadFile(col, files, type) {
				let that = this
				let url = this.opServer + '/wxUpload.php?uploadcol=' + col.name + '&id=' + this.realId + '&rid=' + this.realRid +
					'&taskid=' + this.edit_taskid + '&nodeid=' + this.edit_nodeid + '&KEY=' + this.edit_key + '&uploadCols=' + this.uploadCols + '&uploadKey=' + this.uploadKey
				if (this.edit_master) {
					url += '&master=' + this.edit_master
				}
				if (that.latitude) { //可以用微信小程序取到经纬度
					url += '&latitude=' + that.latitude + '&longitude=' + that.longitude + '&accuracy=' + that.accuracy
					this.getAddressName(that.longitude, that.latitude, function(address) {
						if (address) {
							url += '&address=' + address
						}
						that.uploadFileSend(col, files, type, url)
					})
				} else {
					this.uploadFileSend(col, files, type, url)
				}
			},
			uploadFileSend(col, files, type, url) {
				let that = this
				var formData = {}
				for (let i = 0; i < files.length; i++) {
					if (files[i].name) {
						formData = {
							'filename': files[i].name
						}
					} else if (files[i].duration) {
						formData = {
							'duration': files[i].duration
						}
					} else {
						formData = {}
					}
					if (getApp().globalData.offline) { //如果是离线模式直接缓存
						that.saveFileToCache(col, url, formData, files[i].path)
						return
					}
					if ((col.type == 'SGFILE' || col.type == 'SGVIDEO') && url.indexOf('latitude') <
						0) { //如果是定位拍照则未获取到经纬度直接缓存，从缓存上传时再次获取经纬度，用于无信号区域拍照
						that.saveFileToCache(col, url, formData, files[i].path)
						uni.showModal({
							title: '提示',
							content: '未获取到定位信息，已缓存，请定位后再上传'
						})
						return
					}
					uni.showLoading({
						title: '正在上传...'
					})
					//#ifdef APP-PLUS||H5
					var header = {}
					//#endif
					// #ifdef MP-WEIXIN
					var header = {
						'Cookie': uni.getStorageSync('cookieKey')
					}
					// #endif
					uni.uploadFile({
						url: url,
						dataType: 'json',
						header: header,
						filePath: files[i].path,
						name: 'upload',
						formData: formData,
						success: function(uploadRes) {
							uni.hideLoading()
							if (uploadRes.statusCode != 200) { //上传失败(应用未正常返回)且不是缓存的照片需要缓存
								uni.showModal({
									content: '上传失败:' + uploadRes.errMsg
								})
								if (type == 'pic') {
									that.saveFileToCache(col, url, formData, files[i].path)
								}
							} else {
								let res = JSON.parse(uploadRes.data)
								if (res.err) {
									uni.showModal({
										title: '提示',
										showCancel: false,
										content: res.err
									});
									return
								}
								if (res.name.alert && !that.fromCache) {
									uni.showModal({
										title: '提示',
										showCancel: false,
										content: res.name.alert
									});
								}
								if (res.state == 1) {
									if (col.data) {
										col.data.push([res.rid, res.fileName, res.type, that.opServer + '/' +
											res.url
										])
									} else if (col.value) {
										that.$set(col, "value", that.opServer + '/' + res.url);
									}
								} else { //上传失败(应用有返回但返回失败)且不是缓存的照片需要缓存
									uni.showModal({
										content: '上传失败:' + res.err
									})
									if (type == 'pic') {
										that.saveFileToCache(col, url, formData, files[i].path)
									}
								}
							}
						},
						fail: function(info) { //上传失败(网络层)且不是缓存的照片需要缓存
							uni.hideLoading()
							uni.showModal({
								content: '上传失败:' + info.errMsg
							})
							if (type == 'pic') {
								that.saveFileToCache(col, url, formData, files[i].path)
							}
						}
					})
				}
			},
			saveFileToCache(col, url, formData, filePath) { //上传不成功时缓存
				var that = this
				var userId = getApp().globalData.userId
				var cachePics = {}
				if (uni.getStorageSync('cachePics')) {
					cachePics = uni.getStorageSync('cachePics')
				}
				if (!cachePics[userId]) {
					cachePics[userId] = {}
				}
				if (!cachePics[userId][this.rid]) {
					cachePics[userId][this.rid] = {}
					cachePics[userId][this.rid]['id'] = this.id
					cachePics[userId][this.rid]['label'] = this.label + '-' + this.name
				}
				if (!cachePics[userId][this.rid][col.name]) {
					cachePics[userId][this.rid][col.name] = []
				}
				let editType = col.type
				cachePics[userId][this.rid][col.name].push({ //此种写法自动转换成为键值对
					url,
					formData,
					filePath,
					editType
				})
				uni.setStorageSync('cachePics', cachePics)
				this.getCachePic()
			},
			getCachePic() {
				var that = this
				var userId = getApp().globalData.userId
				var cachePics = uni.getStorageSync('cachePics')
				if (cachePics && cachePics[userId]) {
					this.cachePics = cachePics[userId][this.rid] || {}
				}
			},
			deleteFormCache() {
				let cacheLists = uni.getStorageSync('cacheLists')
				if (cacheLists) {
					delete cacheLists[this.userId][this.id][this.rid]
					uni.setStorageSync('cacheLists', cacheLists)
				}
				uni.showToast({
					title: '取消离线成功'
				})
				this.isCached = false
				uni.$emit('updateCacheState', {
					rid: this.rid,
					isCached: this.isCached
				})
			},
			saveFormToCache() {
				this.formRes.datas = this.formData
				let cacheLists = uni.getStorageSync('cacheLists')
				let userId = this.userId
				let colTitle = this.cacheData.colTitle
				if (cacheLists) {
					if (cacheLists[userId]) {
						if (!cacheLists[userId][this.id]) {
							cacheLists[userId][this.id] = {
								label: this.label
							}
						}
					} else {
						cacheLists[userId] = {}
						cacheLists[userId][this.id] = {
							label: this.label
						}
					}
				} else {
					cacheLists = {}
					cacheLists[userId] = {}
					cacheLists[userId][this.id] = {
						label: this.label
					}
				}
				cacheLists[userId][this.id][this.rid] = {
					listData: this.cacheData.listData,
					colTitle: colTitle,
					formData: this.formRes,
					editUrl: this.cacheData.editURL
				}
				cacheLists[userId][this.id][this.rid].formData = this.formRes
				cacheLists[userId][this.id][this.rid].tabData = {}
				uni.setStorageSync('cacheLists', cacheLists)
				for (let item of this.formData) {
					if (item['type'] == 'ZC' || item['type'] == 'FORMTAB') {
						this.$refs[item['name']][0].saveListToCache(this.id)
					}
				}
				uni.showToast({
					title: '离线缓存成功'
				})
				this.isCached = true
				uni.$emit('updateCacheState', {
					rid: this.rid,
					isCached: this.isCached
				})
			},
			showHelpTip(item) {
				uni.showModal({
					title: '帮助提示',
					content: item.help.content,
					showCancel: false
				})
			},
			checkRef(item) {
				if (item.ref) {
					let isJson = false
					let checkArr = []
					try {
						checkArr = JSON.parse(item.ref)
						isJson = true
					} catch (e) {
						isJson = false
					}
					if (!isJson) {
						// #ifndef MP-WEIXIN
						let checkFunc = eval(item.ref)
						let checkRes = checkFunc(item.value)
						if (checkRes) {
							uni.showModal({
								title: '提示',
								content: checkRes.error || checkRes.alert
							})
							if (checkRes.error) {
								item.value = ''
							}
						}
						// #endif
					} else {
						for (let checkItem of checkArr) {
							let val = parseFloat(item.value)
							let numberBool = (!checkItem.range[0] || val >= checkItem.range[0]) && (!checkItem.range[1] ||
								val <= checkItem.range[1])
							//let numberBool = (checkItem.out&&(val<checkItem.range[0]||val>checkItem.range[1]))||(!checkItem.out&&((!checkItem.range[0]||val>=checkItem.range[0])&&(!checkItem.range[1]||val<=checkItem.range[1])))
							//let stringBool = (checkItem.type=='string')&&(((checkItem.check=='in'||checkItem.check=='')&&(checkItem.range.includes(item.value)))||(checkItem.check=='out'&&(!checkItem.range.includes(item.value))))
							if (numberBool) {
								uni.showModal({
									title: '提示',
									content: checkItem.error || checkItem.alert
								})
								if (checkItem.error) {
									item.value = ''
								}
								break
							}
						}
					}
				}
			},
			clickMap(item, e) {
				if (item.disabled) {
					return
				}
				item.value = e.detail.longitude + ',' + e.detail.latitude
				uni.createMapContext(item['name']).addMarkers({
					markers: [{
						id: 1,
						latitude: e.detail.latitude,
						longitude: e.detail.longitude,
						width: 20, //宽
						height: 30, //高
					}],
					clear: true
				})
			},
			getFieldset() {
				this.fieldset = {}
				this.showForm = {}
				let currFieldset = ''
				let open = true
				for (let item of this.formData) {
					if (item.type == 'fieldset') {
						open = item.open
						this.$set(this.fieldset, item.name, {
							show: open,
							data: []
						})
						currFieldset = item.name
					} else if (currFieldset) {
						this.showForm[item.name] = open
						this.fieldset[currFieldset]['data'].push(item.name)
					} else {
						this.showForm[item.name] = true
					}
				}
			},
			setFieldsetShow(name) {
				let show = this.fieldset[name]['show']
				this.fieldset[name]['show'] = !show
				for (let item of this.fieldset[name]['data']) {
					this.showForm[item] = !show
				}
			},
			deleteUploadCachePic(col, path) { //缓存的上传后直接删除缓存不提示
				var userId = getApp().globalData.userId
				var cachePics = uni.getStorageSync('cachePics')
				if (cachePics) {
					let idx = -1
					cachePics[userId][this.rid][col].forEach((item, index) => {
						if (item.filePath == path) {
							idx = index
						}
					})
					cachePics[userId][this.rid][col].splice(idx, 1)
					if (Object.keys(cachePics[userId][this.rid][col]).length == 0) {
						delete cachePics[userId][this.rid][col]
					}
					if (Object.keys(cachePics[userId][this.rid]).length == 2) {
						delete cachePics[userId][this.rid]
					}
					this.cachePics = cachePics[userId][this.rid] || {}
					uni.setStorageSync('cachePics', cachePics)
				}
			},
			deleteCachePic(col, path) {
				var that = this
				uni.showModal({
					title: '提示',
					content: '确认删除未上传的照片吗',
					success(res) {
						if (res.confirm) {
							that.deleteUploadCachePic(col, path)
						}
					}
				})
			},
			uploadCache(col) { //上传缓存图片，需要避免反复点击反复上传
				var that = this
				if (!this.startSendCachePic) return
				for (let item of this.cachePics[col.name]) {
					if (item.url.indexOf('latitude') < 0) {
						if (this.gpsRequried && !this.latitude) {
							uni.showModal({
								title: '提示',
								content: '未获取到定位信息，请先定位!'
							})
							return
						}
						let url = item.url
						if (that.latitude) {
							url += '&latitude=' + that.latitude + '&longitude=' + that.longitude
							this.getAddressName(that.longitude, that.latitude, function(address) {
								if (address) {
									url += '&address=' + address
								}
								that.uploadCacheItem(item, col, url)
							})
						}
					} else if (item.url.indexOf('address') < 0) {
						let lng = this.getQueryString(item.url, 'longitude')
						let lat = this.getQueryString(item.url, 'latitude')
						this.getAddressName(lng, lat, function(address) {
							let url = item.url
							if (address) {
								url += '&address=' + address
							}
							that.uploadCacheItem(item, col, url)
						})
					} else {
						this.uploadCacheItem(item, col)
					}
				}
				this.startSendCachePic = true
			},
			uploadCacheItem(item, col, url = '') {
				if (col.type == 'SGFILE' || col.type == 'SGVIDEO') {
					// #ifdef MP-WEIXIN||APP-PLUS
					if (!checkOpenGPSService()) {
						this.showErr('请开启位置服务后再上传!')
						return
					}
					//#endif
					if (!getApp().globalData.latitude) {
						this.showErr('请定位后再上传!')
						return
					}
				}
				var that = this
				let itemUrl = item.url
				if (url) {
					itemUrl = url
				}
				uni.showLoading({
					title: '正在上传...',
				})
				//#ifdef APP-PLUS||H5
				var header = {}
				//#endif
				// #ifdef MP-WEIXIN
				var header = {
					'Cookie': uni.getStorageSync('cookieKey')
				}
				// #endif
				uni.uploadFile({
					url: itemUrl, //仅为示例，非真实的接口地址
					dataType: 'json',
					header: header,
					filePath: item.filePath,
					name: 'upload',
					formData: item.formData,
					success: function(uploadRes) {
						uni.hideLoading()
						if (uploadRes.statusCode != 200) {
							uni.showModal({
								content: '上传失败:' + uploadRes.errMsg
							})
						} else {
							let res = JSON.parse(uploadRes.data)
							if (res.err) {
								that.showErr(res.err)
								return
							}
							if (res.name.alert) {
								uni.showModal({
									title: '提示',
									showCancel: false,
									content: res.name.alert
								});
							}
							if (res.state == 1) {
								if (col.data) { //通用附件
									col.data.push([res.rid, res.fileName, res.type, that.opServer + '/' + res
										.url
									])
								} else if (col.value) { //嵌入图片
									that.$set(col, "value", that.opServer + '/' + res.url);
								}
								that.deleteUploadCachePic(col.name, item.filePath)
							} else {
								uni.showModal({
									content: '上传失败:' + res.extra.info
								})
							}
						}
					},
					fail: function(info) {
						uni.hideLoading()
						uni.showModal({
							content: '上传失败:' + info.errMsg
						})
					}
				})
			},
			download(e) {
				let that = this
				uni.downloadFile({
					url: this.opServer + "/wxDownload.php?id=" + this.id + "&fileid=" + e[0],
					header: {
						'Cookie': uni.getStorageSync('cookieKey')
					},
					success: function(res) {
						uni.saveFile({
							tempFilePath: res.tempFilePath, //临时路径
							success: function(res) {
								uni.openDocument({
									filePath: res.savedFilePath,
									success: function(res) {
										console.log('打开文档成功');
									}
								})
							}
						})
					}
				})
			},
			getQueryString(url, name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
				var r = url.match(reg);
				if (r != null) return unescape(r[2]);
				return null;
			},
			closePage() {
				if (this.btnInterval) {
					clearInterval(this.btnInterval);
					this.btnInterval = null;
				}
				if (this.gpsInterval) {
					clearInterval(this.gpsInterval);
					this.gpsInterval = null;
				}
				getApp().globalData.gpsNeed = 0
			},
			callPhone(number) {
				uni.makePhoneCall({
					phoneNumber: number
				})
			}
		},
		onPullDownRefresh() {
			//下拉刷新如果是从cache读取的需要取消fromCache=true属性
			if (!getApp().globalData.offline) {
				this.fromCache = false
			}
			this.initEdit()
		},
		onUnload: function() { //微信小程序中有效
			this.closePage()
		},
		onBackPress: function(e) { //APP中使用，微信小程序中无效
			this.closePage()
		},
		onPageScroll(e) {
			const scrollThreshold = 10; // 设置滚动阈值，单位：像素
			if (e.scrollTop <= 0) {
				this.showSubPage = true;
				return;
			}
			let scrollTop = e.scrollTop;
			if (Math.abs(scrollTop - this.oldScrollTop) < scrollThreshold) {
				return;
			}
			if (this.oldScrollTop === undefined) {
				this.oldScrollTop = e.scrollTop;
			}
			if (this.oldScrollTop > e.scrollTop) {
				this.showSubPage = true;
			} else {
				this.showSubPage = false;
			}
			this.oldScrollTop = e.scrollTop;
		}
	}
</script>

<style scoped lang="scss">
	view {
		line-height: inherit
	}

	.main-top {
		position: fixed;
		left: 0;
		top: 0;
		z-index: 12;
		width: 100%;
		background-color: #fafbfc;
	}

	.subPageContainer {
		display: flex;
		background-color: #fff;
		align-items: center;
		width: 100%;
		flex: 1;
		z-index: 12;
		padding: 10rpx 0rpx;
		flex-direction: row;
		flex-wrap: wrap;
		border-bottom: 1rpx solid #eee;
	}

	.subPageButton {
		background-color: #f6f5f9;
		color: #0fAEFF;
		margin: 5rpx;
		padding: 0rpx 15rpx;
		font-size: 25rpx;
	}

	.form-box-col {
		background-color: #fff;
		padding: 20rpx;
		display: block;
		flex-direction: column;
		align-items: center;
		width: 100%;
		font-size: 28rpx;
		border-bottom: 1rpx solid #eee;
	}

	.form-box-row {
		padding: 20rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		font-size: 28rpx;
		background: #fff;
	}

	.form-box-title {
		display: flex;
		align-items: center;
		flex-shrink: 0;
		margin-right: 20rpx;
		font-size: 30rpx;
		font-weight: 500;
	}

	.form-box-content {
		width: 100%;
		display: flex;
		flex-direction: row;
		justify-content: flex-end;
		align-items: center;
		min-height: 70rpx;
	}

	.form-box-content-left {
		margin-top: 10rpx;
		width: 100%;
		justify-content: flex-start;
	}

	.disabled {
		color: #999
	}

	.attachfile {
		display: flex;
		flex-direction: row;
		align-items: center;
		margin-top: 15rpx;
		flex-wrap: wrap;
	}

	.attach-flag {
		width: 190rpx;
		left: 0;
		top: 40%;
		margin: 0 10rpx;
		background-color: darkorange;
		position: absolute;
		text-align: center;
		font-size: 25rpx;
		font-weight: 700;
		color: #fff;
	}


	.attach-failed {
		width: 100%;
		left: 0;
		top: 40%;
		position: absolute;
		text-align: center;
		font-size: 30rpx;
		font-weight: 700;
	}

	.attach-del {
		position: absolute;
		right: 5px;
		top: 5px;
		background: red;
	}

	.btnContainer {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		z-index: 90;
		position: fixed;
		bottom: 0rpx;
		background-color: #f6f5f9;
		height: 90rpx;
	}

	.searchBtn {
		padding: 0;
		margin: 0 5rpx;
		height: 70rpx;
		font-size: 28rpx;
		border-radius: 10rpx;
		width: 220rpx;
		background-color: #409EFF;
		color: #fff;
	}

	.selectBtn {
		padding: 0 5px;
		height: 70rpx;
		font-size: 28rpx;
		border-radius: 50rpx;
		width: 200rpx;
	}

	/* 抽屉选项 */
	.optionContainer {
		display: flex;
		align-items: center;
		width: 100%;
		flex: 1;
		flex-direction: row;
		flex-wrap: wrap;
	}

	.option-filter {
		margin: 0 50rpx 10rpx 10rpx;
		background-color: #fff; //#f6f5f9;
		border: 0.5rpx solid #aaa;
		width: 100%;
		height: 48rpx;
		border-radius: 27rpx;
		padding-left: 20rpx;
		padding-right: 150rpx;
	}

	.optionBtn {
		padding: 0rpx 2rpx;
		margin: 10rpx 10rpx;
		line-height: 50rpx;
		height: 50rpx;
		font-size: 22rpx;
		border-radius: 25rpx;
		width: 170rpx;
		background-color: #f6f6f6;
		// border: 1px solid grey;
		text-overflow: ellipsis;
		font-weight: 500;
	}

	.optionSelect {
		padding: 0rpx 2rpx;
		margin: 10rpx 10rpx;
		line-height: 50rpx;
		height: 50rpx;
		font-size: 22rpx;
		border-radius: 25rpx;
		width: 170rpx;
		background-color: #d9ecff;
		// border:1px solid red;
		color: #409EFF;
		text-overflow: ellipsis;
		font-weight: 500;
	}

	/* 中间弹出层提示窗口 */
	.uni-tip {
		/* #ifndef APP-NVUE */
		display: flex;
		flex-direction: column;
		/* #endif */
		padding: 15px;
		width: 300px;
		background-color: #fff;
		border-radius: 10px;
	}

	.uni-tip-title {
		margin-bottom: 10px;
		text-align: center;
		font-weight: bold;
		font-size: 16px;
		color: #333;
	}

	.uni-tip-content {
		font-size: 14px;
		color: #999;
	}

	.uni-tip-group-button {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		margin-top: 20px;
	}

	.uni-tip-button {
		flex: 1;
		text-align: center;
		font-size: 14px;
		color: #3b4144;
		font-weight: bold;
	}

	/* 底部更多按钮 */
	.more-button-box {
		background-color: #fff;
		display: flex;
		flex-wrap: wrap;
		padding: 20rpx;
		justify-content: space-between;
	}

	.hide-more-button {
		background-color: #fff;
		height: 90upx;
		line-height: 90upx;
		border-top: 1px #f5f5f5 solid;
		text-align: center;
		color: #999;
	}

	/***********附件处理*********/
	.attach-title {
		position: relative;
		padding: 10rpx 20rpx;
		width: 100%;
	}

	.uploadButton {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 45rpx;
		font-size: 25rpx;
		border-radius: 30rpx;
		min-width: 150rpx;
		background-color: #EFEFF4;
		border: 1px solid grey;
	}

	image,
	video {
		width: 190rpx;
		height: 190rpx;
		margin: 10rpx 10rpx;
		position: relative;
	}

	button::after {
		border: none;
	}
</style>