<template>
	<view>
		<view id="mapContainer" style="z-index: 1;" :style="{height: height ,width: width}" :prop="sender" :change:prop="handleAction">
		</view>
	</view>
</template>
<script module="leaflet" lang="renderjs">
	import '@/libs/leaflet/leaflet.css';
	import '@/libs/leaflet/Leaflet.markercluster/MarkerCluster.css';
	import '@/libs/leaflet/Leaflet.markercluster/MarkerCluster.Default.css';
	import L from '@/libs/leaflet/leaflet'
	import '@/libs/leaflet/leaflet.ChineseTmsProviders.js'
	import '@/libs/leaflet/leaflet.mapCorrection.js'
	import '@/libs/leaflet/terraformer'
	import Terraformer from '@/libs/leaflet/terraformer-wkt-parser'
	import '@/libs/leaflet/Leaflet.markercluster/leaflet.markercluster.js';
	export default {
		data() {
			return {
				map: null,
				keyTianDi: 'fbadfac86e6fd50f4e502422a5606c2a',
				ownerInstance: {},
				markers1: [],
				latitude: '',
				longitude: '',
				showOnly: '',
				markersItem: [],
				first:true
			}
		},
		mounted() {
			this.handleCreate()
		},
		methods: {
			handleAction(newValue, oldValue, ownerInstance, instance) {
				this.ownerInstance = ownerInstance
				this.markers1 = newValue.markers
				this.latitude = newValue.lat
				this.longitude = newValue.lng
				this.showOnly = newValue.onlyShow
				if(newValue.handle){
					this.resetCenter1(newValue.handle.props)
				}
				this.handleExecute()
			},
			resetCenter1(latLng){
				this.map.setView(latLng,this.map.getZoom())
			},
			handleExecute() {
				this.clearMarkers()
				if (!this.latitude)
					return
				if (this.showOnly) {
					let markerIcon = L.icon({
						iconUrl: "static/img/mark_r_64.png",
						iconSize: [40, 40],
						iconAnchor: [20, 30],//图标 "tip" 的坐标（相对于其左上角）。图标将被对齐，使该点位于标记的地理位置。如果指定了尺寸，默认为居中，也可以在CSS中设置负的边距。
						popupAnchor: [0, -20] //图标 "tip" 的坐标（相对于其左上角）。图标将被对齐，使该点位于标记的地理位置。如果指定了尺寸，默认为居中，也可以在CSS中设置负的边距。
					});
					let mapMarker = L.marker([this.latitude, this.longitude], {
						icon: markerIcon,
					})
					
					mapMarker.addTo(this.map);
					this.markersItem.push(mapMarker)
					if(this.first){
						this.map.setView([this.latitude, this.longitude],17)
						this.first = false
					}
					else{
						this.map.setView([this.latitude, this.longitude],this.map.getZoom())
					}
					// this.map.dragging.disable();
					return
				}
				let markerIcon = L.icon({
					iconUrl: "static/img/now.png",
					iconSize: [30, 30], //图标图像的尺寸，单位是像素。
				});
				let nowMarker = L.marker([this.latitude, this.longitude], {
					icon: markerIcon,
				})
				nowMarker.addTo(this.map);
				this.markersItem.push(nowMarker)
				for (let marker of this.markers1) {
					let icon = 'static/img/'+marker.icon
					let markerIcon = L.icon({
						iconUrl: icon,
						iconSize: [40, 40],
						iconAnchor: [20, 30],//图标 "tip" 的坐标（相对于其左上角）。图标将被对齐，使该点位于标记的地理位置。如果指定了尺寸，默认为居中，也可以在CSS中设置负的边距。
						popupAnchor: [0, -20], //图标 "tip" 的坐标（相对于其左上角）。图标将被对齐，使该点位于标记的地理位置。如果指定了尺寸，默认为居中，也可以在CSS中设置负的边距。
					});
					let mapMarker = L.marker([marker.latitude, marker.longitude], {
						icon: markerIcon,
					})
					this.markersItem.push(mapMarker)
					var popup = L.popup().setContent(marker.content)
					mapMarker.bindPopup(popup).openPopup()
					mapMarker.addTo(this.map);
					mapMarker.on('popupopen', (e) =>{
						this.ownerInstance.callMethod('clickMarker',marker)
						mapMarker.getPopup()._container.addEventListener('click', () => {
							let url = 'edit?id=' + marker.zid + '&rid=' + marker.rid
							uni.navigateTo({
								url: url
							})
						});
					});
				}
			},
			handleCreate() {
				this.map = L.map('mapContainer', {
					attributionControl: false,
					zoomControl: false,
					detectRetina: true
				}).setView([30.676397, 104.072331], 5.5)
				var layer = this.handleInitializeLayer()
				layer.addTo(this.map)
			},
			handleInitializeLayer() {
				var tianImage = L.tileLayer.chinaProvider('TianDiTu.Normal.Map', {
					key: this.keyTianDi
				})
				//天地图注记
				var tianCia = L.tileLayer.chinaProvider('TianDiTu.Normal.Annotion', {
					key: this.keyTianDi
				})

				// 天地图图组
				var tiandiMap = L.layerGroup([tianImage, tianCia])

				return tiandiMap
			},
			clearMarkers() {
				for (var i = 0; i < this.markersItem.length; i++) {
					this.map.removeLayer(this.markersItem[i]);
				}
				this.markersItem = []
			}
		}
	}
</script>
<script>
	export default {
		props: {
			lat: {
				type: String,
				default: '',
			},
			lng: {
				type: String,
				default: '',
			},
			markers: {
				type: Array,
				default: ()=>{
					return []
				}
			},
			onlyShow: {
				type: Boolean,
				default: false
			},
			height: {
				type: String,
				default: '300px',
			},
			width: {
				type: String,
				default: '100%',
			}
		},
		data() {
			return {
				sender: {},
			}
		},
		watch: {
			markers() {
				this.sender = {
					markers: this.markers,
					lat: this.lat,
					lng: this.lng,
					onlyShow: this.onlyShow
				}
			},
			lat() {
				this.sender = {
					markers: this.markers,
					lat: this.lat,
					lng: this.lng,
					onlyShow: this.onlyShow
				}
			},
			lng() {
				this.sender = {
					markers: this.markers,
					lat: this.lat,
					lng: this.lng,
					onlyShow: this.onlyShow
				}
			}
		},
		mounted() {
			// this.sender = {}
			this.sender = {
				markers: this.markers,
				lat: this.lat,
				lng: this.lng,
				onlyShow: this.onlyShow
			}
		},
		methods: {
			resetCenter(lat,lng){
				this.sender = {
					handle:{'name':'resetCenter',props:[lat,lng]},
					markers: this.markers,
					lat: this.lat,
					lng: this.lng,
					onlyShow: this.onlyShow
				}
			},
			clickMarker(marker){
				this.$emit('clickMarker',marker)
			}
		}

	}
</script>
<style>

</style>