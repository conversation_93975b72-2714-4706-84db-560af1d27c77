<template>
	<view class="wrap">
		<view @click="showDeviceList=true" class="justify-content-item tn-text-bold tn-text-lg"
			style="align-self: flex-start;margin-left: 22.5rpx;margin-top: 20rpx;">
			{{deviceLabel}}<text class="tn-icon-sequence" style="font-size: 30rpx;margin-left: 10rpx;"></text>
		</view>
		<view class="items">
			<view v-for="item,index in detailData" class="item" :key="index">
				<view class="item-label"><mdi-icon :icon="item.icon" :color="item.color" size="43r"
						style="margin-right: 16rpx;" /><text class="title">{{item.label}}</text></view>
				<view v-for="(data,idx) in item.data" class="data" :key="idx">
					<text style="color:grey">{{data.label}}</text>
					<text>{{resData[data.key]}}</text>
				</view>
			</view>
		</view>
		<tn-select v-model="showDeviceList" mode="multi-auto" childName="data" @confirm="comfirmPick"
			:list="list"></tn-select>
	</view>
</template>

<script>
	import mdiIcon from 'pagesA/components/mdi-icon/components/mdi-icon/mdi-icon.vue'
	export default {
		components: {
			mdiIcon
		},
		data() {
			return {
				showDeviceList: false,
				selectedIndex: 0,
				deviceRid: '',
				deviceLabel: '请选择',
				deviceList: [{
						rid: 'asdasd',
						label: '设备1'
					},
					{
						rid: 'gasdgas',
						label: '设备2'
					}
				],
				resData: {
					CAPSTATUS: '',
					CAPV: '',
					VL: '',
					VH: '',
					BRACELETS: '',
					DGSTATUS: '',
					DGHEIGHT: '',
					HOOK1: '',
					HOOK2: '',
					HOOK1V: '',
					HOOK2V: '',
					H2S: '',
					CH4: '',
					O2: '',
					CO: '',
					GASV: '',
					OPESTATUS: '',
					SETSV: ''
				},
				list: [
				],
				detailData: [{
						label: '安全帽',
						icon: 'mdi-racing-helmet',
						color: '#FF4500',
						data: [{
								label: '安全帽状态',
								key: 'CAPSTATUS'
							},
							{
								label: '安全帽电压',
								key: 'CAPV'
							}
						]
					},
					{
						label: '触电',
						icon: 'mdi-flash',
						color: '#FFD700',
						data: [{
								label: '弱电场',
								key: 'VL'
							},
							{
								label: '强电场',
								key: 'VH'
							},
							{
								label: '手环及电压',
								key: 'BRACELETS'
							}
						]
					},
					{
						label: '登高',
						icon: 'mdi-hiking',
						color: '#8B5A2B',
						data: [{
								label: '登高状态',
								key: 'DGSTATUS'
							},
							{
								label: '登高高度',
								key: 'DGHEIGHT'
							},
							{
								label: '挂钩1',
								key: 'HOOK1'
							},
							{
								label: '挂钩2',
								key: 'HOOK2'
							},
							{
								label: '挂钩1电压',
								key: 'HOOK1V'
							},
							{
								label: '挂钩2电压',
								key: 'HOOK2V'
							}
						]
					},
					{
						label: '气体',
						icon: 'mdi-tailwind',
						color: '#1C86EE',
						data: [{
								label: 'H₂S',
								key: 'H2S'
							},
							{
								label: 'CH₄',
								key: 'CH4'
							},
							{
								label: 'O₂',
								key: 'O2'
							},
							{
								label: 'CO',
								key: 'CO'
							},
							{
								label: '气感电压',
								key: 'GASV'
							}
						]
					},
					{
						label: '主机',
						icon: 'mdi-desktop-tower',
						color: '#1C1C1C',
						data: [{
								label: '开关机状态',
								key: 'OPESTATUS'
							},
							{
								label: '主控电压',
								key: 'SETSV'
							}
						]
					}
				]
			}
		},
		onPullDownRefresh() {
			this.getDeviceList()
			this.getUserInfo()
		},
		onLoad(){
			this.getDeviceList()
			this.getUserInfo()
		},
		methods: {
			comfirmPick(e) {
				this.deviceRid = e[1].value
				this.deviceLabel = e[0].label+'---'+e[1].label
				this.bindDevice()
				this.getDetail()
			},
			getDetail(){
				this.xAjax({
					url: 'wxEdit.php?app=4&id=safetyDeviceBranch&rid='+this.deviceRid,
					allwaysCall:true
				}, (res) =>{
					if(res.datas){
						for(let item of res.datas){
							this.resData[item.name] = item.value
						}
					}else{
						uni.showToast({
							title:'获取设备信息失败'
						})
					}
				})
			},
			getUserInfo(){
				this.xAjax({
					url: 'app/getSafetyDevice.php',
					allwaysCall:true
				}, (res) =>{
					if(res.code){
						this.deviceLabel = res.leader+'---'+res.code
						this.deviceRid = res.code
						this.getDetail()
					}
				})
			},
			getDeviceList(){
				this.xAjax({
					url: 'app/getSafetyDeviceList.php',
					allwaysCall:true
				}, (res) =>{
					this.list = res
				})
			},
			bindDevice(){
				this.xAjax({
					url: 'app/bindSafetyDevice.php?code='+this.deviceRid,
					allwaysCall:true
				}, (res) =>{
					if(res.ok!=1){
						uni.showToast({
							title:'绑定失败'
						})
					}
				})
			}
		}
	}
</script>

<style scoped>
	.wrap {
		display: flex;
		flex-direction: column;
		justify-content: start;
		align-items: center;
		min-height: 100vh;
		background: #f5f5f5;
		padding-bottom: 40rpx;
	}

	.item {
		width: 352.5rpx;
		position: relative;
		display: flex;
		flex-direction: column;
		margin-top: 20rpx;
		margin-left: 15rpx;
		background-color: #fff;
		padding: 20rpx;
		border-radius: 20rpx;
		/* float:left; */
	}

	.items {
		width: 750rpx;
		display: flex;
		flex-wrap: wrap;
	}

	.item-label {
		font-size: 40rpx;
		margin-bottom: 20rpx;
	}

	.data {
		height: 50rpx;
		line-height: 50rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		width: 100%;
	}

	.title {
		font-weight: 600;
	}

	/* .title::before{
		position: absolute;
		content: '';
		height: 6rpx;
		margin-top: 50rpx;
		width: 80rpx;
		background-color: #bebebe;
	} */
</style>