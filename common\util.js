function formatTime(time) {
	if (typeof time !== 'number' || time < 0) {
		return time
	}

	var hour = parseInt(time / 3600)
	time = time % 3600
	var minute = parseInt(time / 60)
	time = time % 60
	var second = time

	return ([hour, minute, second]).map(function (n) {
		n = n.toString()
		return n[1] ? n : '0' + n
	}).join(':')
}

function formatLocation(longitude, latitude) {
	if (typeof longitude === 'string' && typeof latitude === 'string') {
		longitude = parseFloat(longitude)
		latitude = parseFloat(latitude)
	}

	longitude = longitude.toFixed(2)
	latitude = latitude.toFixed(2)

	return {
		longitude: longitude.toString().split('.'),
		latitude: latitude.toString().split('.')
	}
}

var dateUtils = {
	UNITS: {
		'年': 31557600000,
		'月': 2629800000,
		'天': 86400000,
		'小时': 3600000,
		'分钟': 60000,
		'秒': 1000
	},
	humanize: function (milliseconds) {
		var humanize = '';
		for (var key in this.UNITS) {
			if (milliseconds >= this.UNITS[key]) {
				humanize = Math.floor(milliseconds / this.UNITS[key]) + key + '前';
				break;
			}
		}
		return humanize || '刚刚';
	},
	format: function (dateStr) {
		var date = this.parse(dateStr)
		var diff = Date.now() - date.getTime();
		if (diff < this.UNITS['天']) {
			return this.humanize(diff);
		}
		var _format = function (number) {
			return (number < 10 ? ('0' + number) : number);
		};
		return date.getFullYear() + '/' + _format(date.getMonth() + 1) + '/' + _format(date.getDay()) + '-' +
			_format(date.getHours()) + ':' + _format(date.getMinutes());
	},
	parse: function (str) { //将"yyyy-mm-dd HH:MM:ss"格式的字符串，转化为一个Date对象
		var a = str.split(/[^0-9]/);
		return new Date(a[0], a[1] - 1, a[2], a[3], a[4], a[5]);
	}
};

function khkv(formData,s){
	let k=[],kv=""
	let a=s.split(",")
	let len=formData.length
	for(let i=0;i<a.length;i++){
		let c=a[i].toUpperCase()
		if(k.indexOf(c)==-1){
			k.push(c)
			for(let j=0;j< len;j++){
				if(formData[j].name==c){
					kv+="-"+c+"="+encodeURIComponent(formData[j].value)
				}
			}
		}
	}
	return kv
}
function getPopRef(formData,s){
	let k=[],kv=""
	let a=s.split(",")
	let len=formData.length
	for(let i=0;i<a.length;i++){
		let c=a[i].toUpperCase()
		if(k.indexOf(c)==-1){
			k.push(c)
			for(let j=0;j< len;j++){
				if(formData[j].name==c){
					kv+=","+c+"^"+encodeURIComponent(formData[j].value);
				}
			}
		}
	}
	if(kv)return '&ref='+kv.substr(1);
	return '';
}

function checkOpenGPSService() {//检查是否开启了定位
	//#ifdef MP-WEIXIN
	return wx.getSystemSetting().locationEnabled
	//#endif
	
	//#ifdef APP-PLUS||H5
	let bool = false
	//android平台
	if (uni.getDeviceInfo().platform == 'android') {
		var context = plus.android.importClass("android.content.Context");
		var locationManager = plus.android.importClass("android.location.LocationManager");
		var main = plus.android.runtimeMainActivity();
		var mainSvr = main.getSystemService(context.LOCATION_SERVICE);
		bool = mainSvr.isProviderEnabled(locationManager.GPS_PROVIDER)
	}
	// ios平台
	if (uni.getDeviceInfo().platform == 'ios') {
		var cllocationManger = plus.ios.import("CLLocationManager");
		var enable = cllocationManger.locationServicesEnabled();
		var status = cllocationManger.authorizationStatus();
		plus.ios.deleteObject(cllocationManger);
		bool = enable && status != 2
	}
	return bool;
	//#endif
}

function gps84ToGcj02(lon, lat) {
	var pi = 3.1415926535897932384626;
	var a = 6378245.0;
	var ee = 0.00669342162296594323;
	var dLat = transformLat(lon - 105.0, lat - 35.0);
	var dLon = transformLon(lon - 105.0, lat - 35.0);
	var radLat = lat / 180.0 * pi;
	var magic = Math.sin(radLat);
	magic = 1 - ee * magic * magic;
	var sqrtMagic = Math.sqrt(magic);
	dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
	dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);
	var mgLat = parseFloat(lat) + parseFloat(dLat);
	var mgLon = parseFloat(lon) + parseFloat(dLon);
	return {
	  "x" : mgLon,
	  "y" : mgLat
	};
}

function gcj02ToWgs84 (lng, lat) {
	var PI = 3.1415926535897932384626;
	var a = 6378245.0;
	var ee = 0.00669342162296594323;
	lat = +lat
	lng = +lng
	let dlat = transformLat(lng - 105.0, lat - 35.0)
	let dlng = transformLon(lng - 105.0, lat - 35.0)
	let radlat = lat / 180.0 * PI
	let magic = Math.sin(radlat)
	magic = 1 - ee * magic * magic
	let sqrtmagic = Math.sqrt(magic)
	dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI)
	dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI)
	let mglat = lat + dlat
	let mglng = lng + dlng
	return [lng * 2 - mglng, lat * 2 - mglat]
}

function transformLat (x, y) {
	var pi = 3.1415926535897932384626;
	var ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
	ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;
	ret += (20.0 * Math.sin(y * pi) + 40.0 * Math.sin(y / 3.0 * pi)) * 2.0 / 3.0;
	ret += (160.0 * Math.sin(y / 12.0 * pi) + 320 * Math.sin(y * pi / 30.0)) * 2.0 / 3.0;
	return ret;
}

function transformLon (x, y) {
  var pi = 3.1415926535897932384626;
  var ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
  ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;
  ret += (20.0 * Math.sin(x * pi) + 40.0 * Math.sin(x / 3.0 * pi)) * 2.0 / 3.0;
  ret += (150.0 * Math.sin(x / 12.0 * pi) + 300.0 * Math.sin(x / 30.0 * pi)) * 2.0 / 3.0;
  return ret;
}

module.exports = {
	formatTime: formatTime,
	formatLocation: formatLocation,
	dateUtils: dateUtils,
	khkv:khkv,
	getPopRef:getPopRef,
	checkOpenGPSService:checkOpenGPSService,
	gps84ToGcj02:gps84ToGcj02,
	transformLat:transformLat,
	transformLon:transformLon,
	gcj02ToWgs84:gcj02ToWgs84
}

