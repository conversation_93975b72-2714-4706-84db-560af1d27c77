export const soterAuth = (page,result)=> {
	if (uni.getStorageSync('isSoterAuth')&&page=='mine') {
		uni.showModal({
			title: '提示',
			content: '已经启用指纹登录,是否取消',
			confirmText: '是',
			cancelText: '否',
			success(res) {
				if (res.confirm) {
					uni.removeStorageSync('isSoterAuth')
				}
			}
		})
		return
	}
	if (!uni.getStorageSync('soterAuth')) {
		uni.showToast({
			icon: 'none',
			title: '未使用账号密码登录,不支持指纹登录'
		})
		return
	}
	uni.checkIsSupportSoterAuthentication({
		success(res) {
			if (!res.supportMode.includes('fingerPrint')) {
				uni.showToast({
					icon: 'none',
					title: '该设备不支持指纹登录'
				})
				return
			} else {
				uni.checkIsSoterEnrolledInDevice({
					checkAuthMode: 'fingerPrint',
					success(res1) {
						if (!res1.isEnrolled) {
							uni.showToast({
								icon: 'none',
								title: '未录入指纹信息,请前往系统设置中设置'
							})
						} else {
							uni.startSoterAuthentication({
								requestAuthModes: ['fingerPrint'], // 指定验证方式
								challenge: 'rzy', // 用于防重放攻击[1](@ref)
								success(res) {
									result()
								}
							})
						}
					}
				})
			}
		}
	});
}
export default { soterAuth };