<template>
	<view>
		<view style="padding-bottom: 140rpx">
			<view v-for="item in formData" :key="item.label">
				<view class="form-box-col">
					<view class="form-box-title">
						<view style="display: flex;flex-direction: row;align-items: center;">
							{{item.label}}
						</view>
					</view>
					<!-- 显示图片 -->
					<view style="display: flex;flex-direction: row;margin-top:10rpx;flex-wrap:wrap">
						<view v-for="(pic,index) in item.data" style="position: relative;display: flex;flex-direction: column;" :key="index">
							<image :src="pic[1]" @tap="preViewImg(pic[1])">
								<!-- 正中间显示图片标识 -->
								<view v-if="pic[2]=='1'" class="attach-pass">
									合格
								</view>
								<view v-else-if="pic[2]=='0'" class="attach-fail">
									不合格
								</view>
							</image>
							<radio-group @change="radioChange($event,pic)">
								<label style="font-size: 8pt;"><radio value="1" :checked="pic[2]=='1'" style="transform:scale(0.7)" />合格</label>
								<label style="font-size: 8pt;"><radio value="0" :checked="pic[2]=='0'" style="transform:scale(0.7)" />不合格</label>
							</radio-group>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data: function() {
			return {
				id: '',
				label: '',//模块名称
				rid: '',
				name: '',//记录名称
				title: '',//顶部显示的节点环节名称
				realId: '',
				realRid: '',
				edit_taskid: '',
				edit_rid: '',
				edit_nodeid: '',
				edit_key: '',
				edit_master: '',
				formData:[]
			}
		},
		onLoad(e) {
			var that = this
			if (e.master) {
				this.edit_master = e.master
			}
			this.initEdit()
		},
		onPullDownRefresh() {
			this.initEdit()
		},
		methods: {
			goBack: function(){
				uni.navigateBack()
			},
			initEdit: function() {
				let that = this
				let url = 'app/showQualityPicture.php?master=' + this.edit_master
				this.xAjax({
					url: url
				}, function(res) {
					uni.setNavigationBarTitle({ //标题要先清空，避免获取服务器数据失败而显示错误信息
						title: res.title
					})
					that.formData = res.data
				})
			},
			preViewImg(url) {//点击图片后预览图片
				uni.previewImage({
					current: 0,
					urls: [url]
				})
			},
			radioChange: function(e, item) {
				let that = this
				this.xAjax({
					url: "app/checkQualityPicture.php?fileid="+item[0]+"&val="+ e.detail.value + "&master=" + that.edit_master
				}, function(res) {
					that.$set(item, 2, e.detail.value)
				})
			}
		},
		onBackPress: function(e) { //APP中使用，微信小程序中无效
			
		}
	}
</script>

<style scoped lang="scss">
	
	view {
		line-height: inherit
	}

	.form-box-col {
		background-color: #fff;
		padding: 20rpx;
		display: block;
		flex-direction: column;
		align-items: center;
		width: 100%;
		font-size: 28rpx;
		border-bottom: 1rpx solid #eee;
	}

	.form-box-title {
		display:flex;
		align-items: center;
		flex-shrink: 0;
		margin-right: 20rpx;
		font-size: 30rpx;
		font-weight: 500;
	}

	.attach-pass {
		width:215rpx;
		left:0;
		top:35%;
		margin:0 10rpx;
		background-color: lawngreen;
		position:absolute;
		text-align:center;
		font-size:25rpx;
		font-weight:700;
		color:#fff;
	}
	
	.attach-fail {
		width:215rpx;
		left:0;
		top:35%;
		margin:0 10rpx;
		background-color: orange;
		position:absolute;
		text-align:center;
		font-size:25rpx;
		font-weight:700;
		color:#fff;
	}

	image,
	video {
		width: 215rpx;
		height: 215rpx;
		margin: 10rpx 10rpx;
		position: relative;
	}

</style>