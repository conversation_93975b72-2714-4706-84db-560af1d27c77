<template>
	<view>
		<!-- 顶部自定义导航栏 -->
		<view  class="main-top" :style="[{ paddingTop: statusHeight + 'px'}]">
			<view
				style="width:100%;padding-left:20rpx;font-size: 30rpx;align-items:center;height:70rpx;display: flex;flex-direction: row;justify-content: flex-start;">
				{{navigationBarTitle}}
			</view>
		</view>
		<!-- 顶部筛选项栏 -->
		<view v-if="filter.length>0" style="position: fixed;z-index: 999; width:100%;align-items:center;background-color: #fff;height:80rpx;display: flex;flex-direction: row;justify-content: flex-start;">
			<view v-for="(item,idx) in filter" @click="showOptionView(item)"
				style="padding-left:20rpx;font-size:30rpx;display: flex;flex-direction: row;align-items: center;">
				<view :style="'width:'+item.width+'rpx'">{{item.label}}</view>
				<uni-icons type="bottom" size="25"></uni-icons>
			</view>
		</view>
		<!--地图容器，注意calc计算中-号前后必须有空格-->
		<!-- #ifndef MP-WEIXIN -->
		<tdtMap ref="tdtMap" :lat="latitude" :lng="longitude" :height="'calc(100vh - 100rpx )'" :markers="markers"
			:onlyShow="false" @clickMarker="clickMarker"></tdtMap>
		<!-- #endif -->
		<!-- #ifdef MP-WEIXIN -->
		<map id="xMap" :style="'width: 100%; height:calc(100vh - '+(filter.length>0?'180':'100')+'rpx);'" :latitude="latitude" :longitude="longitude" :markers="markers"  
		 :scale="scale" show-location @markertap="markertap" @callouttap="callouttap" >
			 <cover-view slot="callout">
				<template v-for="(item,i) in allSites">
					<cover-view class="callout" :marker-id="i">{{item.content.replace(/\\n/g,'\n')}}</cover-view>
				</template>
			 </cover-view>
		</map>
		<!-- #endif -->
		<!-- 筛选选项栏 -->
		<view v-if="showOption" @click="showOption=false" :style="{top:'calc(150rpx + '+statusHeight+'px)' }"
			style="position:fixed;z-index:99;width:750rpx;padding:0 15rpx 15rpx 15rpx; align-items:center; background-color: #fff;display: flex;flex-direction: row;justify-content:flex-start;flex-wrap: wrap;">
			<view v-for="(item,idx) in options" :key="idx">
				<view @click="clickOption(item)"
					style="background-color: #409EFF;color:#fff;margin:15rpx;padding-left:14rpx;padding-right: 14rpx; line-height: 50rpx;height: 50rpx;font-size: 28rpx;border-radius: 15rpx;">{{item.label}}</view>
			</view>
		</view>
		<!-- 底部工具栏 -->
		<view :style=" 'bottom:'+bottomHight+'px' "
			style="position:fixed;padding:0 25rpx;z-index:999;width:750rpx; align-items:center; background-color: #fff;height:100rpx;display: flex;flex-direction: row;justify-content:space-around;">
			<view v-for="(item,idx) in menu" @click="clickMenu(item)"
				style="font-size:30rpx;display: flex;flex-direction: row;align-items: center;">
				<!-- <image :src="'../static/img/' + item.img" style="width:40rpx;" mode="widthFix" /> -->
				<view>{{item.label}}</view>
			</view>
			<input @input="siteFilter" @confirm="siteFilter" v-model="keyword"
				style="padding-left:20rpx;width:300rpx;height:60rpx;border-radius:30rpx;border:1rpx solid #ddd"
				placeholder="输入关键字搜索">
		</view>
		<!-- 中间的弹窗提示框,对比showModal方便关闭，例如打开GPS搜星成功后可以关闭提示 -->
		<uni-popup ref="showtip" type="center" :mask-click="true"><!-- 蒙版点击是否关闭弹窗 -->
			<view class="uni-tip">
				<text class="uni-tip-title">{{tipTitle}}</text>
				<text class="uni-tip-content">{{ tipContent }}</text>
				<view class="uni-tip-group-button">
					<text class="uni-tip-button" @click="closeTip()">确定</text>
				</view>
			</view>
		</uni-popup>
		<!-- 选择导航 -->
		<tn-picker mode="selector" v-model="showChooseMap" :defaultSelector="[0]" @confirm="goMap" :range="mapSelector"></tn-picker>
	</view>
</template>

<script>
	// #ifndef MP-WEIXIN
	import tdtMap from '@/components/tdt-map/tdt-map.vue'
	// #endif
	var checkOpenGPSService = require('../common/util.js').checkOpenGPSService
	var gps84ToGcj02 = require('../common/util.js').gps84ToGcj02
	export default {
		// #ifndef MP-WEIXIN
		components: {
			tdtMap
		},
		// #endif
		created() {
			// #ifdef MP-WEIXIN
			this.statusHeight = uni.getMenuButtonBoundingClientRect().top
			// #endif
			// #ifdef APP-PLUS
			this.statusHeight = 40
			// #endif
			// #ifdef H5
			this.bottomHight = 50
			// #endif
		},
		data() {
			return {
				mapSelector:["高德地图","腾讯地图", "百度地图"],
				showChooseMap:false,
				statusHeight: 0,
				navigationBarTitle: '地图',
				showOption: false,
				bottomHight: 0,
				filter: [],
				options: [],
				currItem: {}, //点击的下拉框对象
				flag: 0,
				lastX: 0,
				lastY: 0,
				menu: [{
						label: "刷新",
						img: "reload.png"
					},
					{
						label: "导航",
						img: "navicate.png"
					},
					{
						label: "切换",
						img: "map.png"
					}
				],
				keyword: '',
				showAll: false,
				gpsInterval: {},
				gisCenter: [], //后台配置的地图中心点
				focusTarget: false,
				map: {},
				longitude: '',
				latitude: '',
				targetLat: '', //点中点位的纬度
				targetLng: '',
				scale: 6, //地图初始缩放,加载时再从currServer获取
				siteName: '', //当前选中点位的名称
				allSites: [],
				markers: [],
				tipTitle: '', //报错弹窗标题
				tipContent: '' //报错弹窗内容
			}
		},
		methods: {
			showOptionView(e) { //显示弹窗下拉框
				if (e.label == this.currItem.label || !this.showOption) { //当切换筛选项时或者选项未显示时
					this.showOption = !this.showOption
				}
				this.options = e.options
				this.currItem = e
			},
			clickOption(e) { //下拉框选中选项
				if (!this.currItem.name) { //用于选择全部时恢复显示的标题
					if (!e.id) {
						this.showOption = false
						return
					}
					this.currItem.name = this.currItem.label
					this.currItem.label = e.label
				} else if (!e.id) {
					this.currItem.label = this.currItem.name
				} else {
					this.currItem.label = e.label
				}
				this.currItem.value = e.id
				this.showOption = false
				var keys = []
				var vals = []
				for (var i = 0; i < this.filter.length; i++) {
					if (this.filter[i].value) {
						keys.push(this.filter[i].key)
						vals.push(this.filter[i].value)
					}
				}
				for (var i = 0; i < this.allSites.length; i++) {
					this.allSites[i].hide = false
					for (var k = 0; k < keys.length; k++) {
						if (this.allSites[i][keys[k]] != vals[k]) {
							this.allSites[i].hide = true
							break
						}
					}
				}
				this.markers = []
				for (var i = 0; i < this.allSites.length; i++) {
					if (!this.allSites[i].hide) {
						if(!this.allSites[i].latitude){
							continue
						}
						this.markers.push({
							id: i,
							zid: this.allSites[i].id, //模块id与地图内置的id冲突，这里用zid
							rid: this.allSites[i].rid, //主键值
							siteName: this.allSites[i].siteName,
							latitude: this.allSites[i].latitude,
							longitude: this.allSites[i].longitude,
							iconPath: '../static/img/' + (this.allSites[i].icon || 'mark_r_64.png'),
							icon:this.allSites[i].icon || 'mark_r_64.png',
							content:this.allSites[i].content,
							width: 40, //宽
							height: 40, //高
							anchor:{x: 0.5, y: 0.75},
							customCallout: {
								anchorY: 8,
								display: 'BYCLICK'
							}
						})
					}
				}
			},
			clickMenu(e) {
				if (e.label == '刷新') {
					this.keyword = ''
					this.loadSites()
				} else if (e.label == '导航') {
					this.navicate()
				} else if (e.label == '切换') {
					if (!getApp().globalData.latitude) {
						this.tipTitle = "定位失败"
						this.tipContent = "请确认已打开网络和定位服务"
						this.$refs.showtip.open()
						return
					}
					if (this.targetLat && !this.focusTarget) {
						this.focusTarget = true
						//#ifdef MP-WEIXIN
						this.map.moveToLocation({
							longitude: Number(this.targetLng),
							latitude: Number(this.targetLat)
						})
						//#endif
						//#ifndef MP-WEIXIN
						this.$refs.tdtMap.resetCenter(Number(this.targetLat),Number(this.targetLng))
						//#endif
					} else {
						this.focusTarget = false
						//#ifdef MP-WEIXIN
						this.map.moveToLocation({
							longitude: getApp().globalData.longitude,
							latitude: getApp().globalData.latitude
						})
						//#endif
						//#ifndef MP-WEIXIN
						this.$refs.tdtMap.resetCenter(getApp().globalData.latitude,getApp().globalData.longitude)
						//#endif
					}
				}
			},
			loadSites() { //获取所有点位
				var that = this;
				this.targetLat = ''
				this.targetLng = ''
				var currServer=uni.getStorageSync('currServer')
				if (currServer.gisCenter) {
					this.gisCenter=currServer.gisCenter.split(",")
					//#ifdef MP-WEIXIN||APP-PLUS
					this.map.moveToLocation({ //必须带经纬度调用，否则经纬度无变化时无法回到当前位置，另一种方法为将纬度加0.000001微小变化
						longitude: Number(that.gisCenter[0]),
						latitude: Number(that.gisCenter[1])
					})
					//#endif
					//#ifdef H5
					this.longitude = this.gisCenter[0]
					this.latitude = this.gisCenter[1]
					//#endif
				}
				let url = 'map/weixin/getAllSites.php'
				this.xAjax({
					url: url,
					hideLoading: true
				}, (res) => { //这种写法that可以用this，function(res){}写法则只能使用that
					if (res.title) {
						that.navigationBarTitle = res.title
					}
					var keyArr = []
					var keyVal = {}
					if (res.filter) {
						this.filter = res.filter
						for (let i = 0; i < res.filter.length; i++) {
							if (res.filter[i].value) { //value有值的是默认选中项，需默认显示相应的点
								this.filter[i].name = this.filter[i].label
								for (let k = 0; k < this.filter[i].options.length; k++) {
									if (this.filter[i].options[k].id == res.filter[i].value) {
										this.filter[i].label = this.filter[i].options[k].label
									}
								}
								keyArr.push(res.filter[i].key)
								keyVal[res.filter[i].key] = res.filter[i].value
							}
						}
					}
					if (res.data) {
						this.allSites = res.data
						this.markers = []
						var show
						for (var i = 0; i < res.data.length; i++) {
							show = true
							if (keyArr.length > 0) { //有默认值
								for (var k = 0; k < keyArr.length; k++) {
									if (keyVal[keyArr[k]] != res.data[i][keyArr[k]]) {
										show = false
									}
								}
							}
							if (show) {
								if(!this.allSites[i].latitude){
									continue
								}
								this.markers.push({
									id: i,
									zid: res.data[i].id, //模块id与地图内置的id冲突，这里用zid
									rid: res.data[i].rid, //主键值
									siteName: res.data[i].siteName,
									latitude: res.data[i].latitude,
									longitude: res.data[i].longitude,
									iconPath: '../static/img/' + (res.data[i].icon || 'mark_r_64.png'),
									icon:res.data[i].icon || 'mark_r_64.png',
									content:res.data[i].content,
									width: 40, //宽
									height: 40, //高
									anchor:{x: 0.5, y: 0.75},
									customCallout: {
										anchorY: 8,
										display: 'BYCLICK'
									}
								})
							}
						}
					}
				})
			},
			siteFilter() {
				var that = this
				var calloutDisplay
				var location = false
				this.markers = []
				for (var i = 0; i < this.allSites.length; i++) {
					if (this.allSites[i].content.indexOf(this.keyword) > -1) {
						if (this.keyword) {
							calloutDisplay = 'ALWAYS'
						} else {
							calloutDisplay = 'BYCLICK'
						}
						if(!this.allSites[i].latitude){
							continue
						}
						this.markers.push({
							id: i,
							zid: this.allSites[i].id, //模块id与地图内置的id冲突，这里用zid
							rid: this.allSites[i].rid, //主键值
							siteName: this.allSites[i].siteName,
							latitude: this.allSites[i].latitude,
							longitude: this.allSites[i].longitude,
							iconPath: '../static/img/' + (this.allSites[i].icon || 'mark_r_64.png'),
							icon:(this.allSites[i].icon || 'mark_r_64.png'),
							content:this.allSites[i].content,
							width: 40, //宽
							height: 40, //高
							anchor:{x: 0.5, y: 0.75},
							customCallout: {
								anchorY: 8,
								display: calloutDisplay
							}
						})
						if (this.keyword && !location) { //通过关键字定位聚焦到第一个匹配的点
							location = true
							this.map.moveToLocation({
								longitude: that.allSites[i].longitude,
								latitude: that.allSites[i].latitude
							})
						}
					}
				}
			},
			closeTip: function() {
				this.$refs.showtip.close()
			},
			clickMarker(marker){
				this.targetLat = marker.latitude
				this.targetLng = marker.longitude
				this.siteName = marker.siteName
			},
			markertap(e) { //点击地图图标后，展示点位信息
				for (let i = 0; i < this.allSites.length; i++) {
					if (i == e.detail.markerId) { //获取当前点中的点位，以便调用导航,markerId是地图插件固有参数
						this.targetLat = this.allSites[i].latitude
						this.targetLng = this.allSites[i].longitude
						this.siteName = this.allSites[i].siteName
					}
				}
				for (let i = 0; i < this.markers.length; i++) {
					if (this.markers[i].id != e.markerId) {
						this.markers[i].customCallout.display = 'BYCLICK'
					} else {
						if (this.markers[i].customCallout.display == 'ALWAYS') {
							this.markers[i].customCallout.display = 'BYCLICK'
						} else {
							this.markers[i].customCallout.display = 'ALWAYS'
						}
					}
				}
			},
			callouttap(e) { //弹出框点击后链接到详情页面
				for (var i = 0; i < this.allSites.length; i++) {
					if (i == e.detail.markerId) {
						let url = 'edit?id=' + this.allSites[i].id + '&rid=' + this.allSites[i].rid
						uni.navigateTo({
							url: url
						})
					}
				}
			},
			goMap(map) {
				map = this.mapSelector[map[0]]
			    let tlongitude = this.targetLng
			    let tlatitude = this.targetLat
				let name = this.siteName
			    // console.log(flongitude + ',' + flatitude + ';' + tlongitude + ',' + tlatitude + ',' + name)
			    var urlCollect = {};
			    const TMAP_KEY = 'FSWBZ-IQR6L-BRDP6-EXPNQ-53ZYJ-7CFBS';
			    if (map == "腾讯地图") {
			        // if (flongitude && flatitude) {
			        //     urlCollect.Android = `qqmap://map/routeplan?type=drive&from=我的位置&fromcoord=${flatitude},${flongitude}&to=${name}&tocoord=${tlatitude},${tlongitude}&referer=${TMAP_KEY}`;
			        //     urlCollect.H5 = `https://apis.map.qq.com/uri/v1/routeplan?type=drive&from=我的位置&fromcoord=${flatitude},${flongitude}&to=${name}&tocoord=${tlatitude},${tlongitude}&policy=1&referer=${TMAP_KEY}`;
			        // } else {
			        urlCollect.Android = `qqmap://map/marker?marker=coord:${tlatitude},${tlongitude};title:${name};addr:${name}&referer=${TMAP_KEY}`;
			        urlCollect.H5 = `https://apis.map.qq.com/uri/v1/marker?marker=coord:${tlatitude},${tlongitude};title:${name};addr:${name}&referer=${TMAP_KEY}`;
			        // }
			
			    } else if (map == "百度地图") {
			        // if (flongitude && flatitude) {
			        //     const uri = `direction?origin=${flatitude},${flongitude}|name:我的位置&destination=${tlatitude},${tlongitude}|name:${name}&mode=driving&region=${name}`;
			        //     urlCollect.H5 = `http://api.map.baidu.com/${uri}&output=html`
			        //     urlCollect.Android = `bdapp://map/${uri}&src=andr.baidu.openAPIdemo`;
			        //     urlCollect.IOS = `baidumap://map/${uri}&src=ios.baidu.openAPIdemo`;
			        // } else {
			        const uri = `marker?location=${tlatitude},${tlongitude}&title=${name}&content=${name}`;
			        urlCollect.H5 = `https://api.map.baidu.com/${uri}&output=html&src=webapp.baidu.openAPIdemo`;
			        urlCollect.Android = `bdapp://map/${uri}&src=andr.baidu.openAPIdemo`;
			        urlCollect.IOS = `baidumap://map/${uri}&src=ios.baidu.openAPIdemo`;
			        // }
			    } else {//高德地图
			        // if (flongitude && flatitude) {
			        //     urlCollect.H5 = `https://uri.amap.com/navigation?from=${flongitude},${flatitude},我的位置&to=${tlongitude},${tlatitude},${name}&mode=driving&policy=1&src=mypage&coordinate=gaode&callnative=1`;
			        //     urlCollect.Android = `amapuri://route/plan/?sid=BGVIS1&slat=${flatitude}&slon=${flongitude}&sname=当前位置&did=BGVID1&dlat=${tlatitude}&dlon=${tlongitude}&dname=${name}&dev=0&t=0`
			        //     urlCollect.IOS = `iosamap://path?sourceApplication=applicationName&sid=BGVIS1&slat=${flatitude}&slon=${flongitude}&sname=当前位置&did=BGVID1&dlat=${tlatitude}&dlon=${tlongitude}&dname=${name}&dev=0&t=0`
			        // } else {
			        urlCollect.H5 = `https://uri.amap.com/marker?position=${tlongitude},${tlatitude}&name=${name}&src=mypage&coordinate=gaode&callnative=1`;
			        urlCollect.Android = `amapuri://route/plan/?sourceApplication=mhc&dlat=${tlatitude}&dlon=${tlongitude}&dname=${name}&dev=0&t=0`//导航
			        urlCollect.IOS = `iosamap://path?sourceApplication=mhc&dlat=${tlatitude}&dlon=${tlongitude}&dname=${name}&dev=0&t=0`
			        // }
			    }
			    var url = ""
			    if (typeof plus === "undefined") {
			        url = urlCollect.H5;
			        window.open(url, "_blank");
			        return;
			    } else if (plus.os.name == "Android") {
			        url = urlCollect.Android;
			    } else {
			        url = map == "腾讯地图" ? urlCollect.Android : urlCollect.IOS;
			    }
				
			    if (url != "") {
			        url = encodeURI(url);
			        plus.runtime.openURL(url, function (e) {
			            if (e.code == 0) {
			                // 打开地图应用成功
			                webix.message("成功打开地图应用");
			            } else {
			                // 打开地图应用失败，提示未安装应用
			                plus.nativeUI.alert("本机未安装指定的地图应用");
			            }
			        });
			    }
			},
			navicate(e) { //调用外部导航APP
				var that = this
				if (this.targetLng == '') {
					this.tipTitle = "提示"
					this.tipContent = "请先选中点位"
					this.$refs.showtip.open()
					return
				}
				//#ifdef MP-WEIXIN
				if (!checkOpenGPSService()) {
					this.tipTitle = "提示"
					this.tipContent = "请先打开定位"
					this.$refs.showtip.open()
					return
				}
				this.map.openMapApp({
					latitude: Number(that.targetLat),
					longitude: Number(that.targetLng),
					destination: that.siteName,
					success() {
						console.log('success!!');
					},
					fail(e) {
						console.log(e);
					}
				})
				//#endif
				//#ifndef MP-WEIXIN
				this.showChooseMap = true
				//#endif
				
			}
		},
		onReady() {
			//#ifndef H5
			this.map = uni.createMapContext("xMap")
			//#endif
			this.loadSites()
		},
		onShow() {
			var that = this
			getApp().globalData.gpsNeed = 1
			this.gpsInterval = setInterval(function() {
				if(getApp().globalData.locationError){
					that.tipTitle = "定位失败"
					that.tipContent = getApp().globalData.locationError
					that.$refs.showtip.open()
				}else if(that.tipTitle == "定位失败"){
					that.$refs.showtip.close()
				}
				if (!that.latitude&&getApp().globalData.latitude) {
					that.latitude = getApp().globalData.latitude
					that.longitude = getApp().globalData.longitude
				}
			}, 3000)
		},
		onHide() {
			getApp().globalData.gpsNeed = 0
			clearInterval(this.gpsInterval);
			this.gpsInterval = null;
		}
	}
</script>
<style>
	.callout {
		width: 540rpx;
		text-align: left;
		background-color: #00c16f;
		color: white;
		border-radius: 20rpx;
		padding: 16rpx;
		padding-bottom: 36rpx;
		white-space: pre-line;
		font-size: 24rpx;
	}

	.main-top {
		position: sticky;
		left: 0;
		top: 0;
		z-index: 999;
		width: 100%;
		background-color: #fafbfc;
	}

	.scroll {
		width: 100%;
		background-color: #ffffff;
		position: absolute;
		top: 80rpx;
		z-index: 20;
		overflow-y: scroll;
		border-bottom-left-radius: 5px;
		border-bottom-right-radius: 5px;
	}

	/* 中间弹出层提示窗口 */
	.uni-tip {
		/* #ifndef APP-NVUE */
		display: flex;
		flex-direction: column;
		/* #endif */
		padding: 15px;
		width: 300px;
		font-size: 16px;
		background-color: #fff;
		border-radius: 10px;
	}

	.uni-tip-title {
		margin-bottom: 10px;
		text-align: center;
		font-weight: bold;
		font-size: 16px;
		color: #333;
	}

	.uni-tip-content {
		font-size: 14px;
		color: #888;
	}

	.uni-tip-group-button {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		margin-top: 20px;
	}

	.uni-tip-button {
		flex: 1;
		text-align: center;
		font-size: 14px;
		color: #3b4144;
		font-weight: bold;
	}
</style>