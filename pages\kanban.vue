<template>
	<view>
		<scroll-view :scroll-x="true" :scroll-with-animation="true" class="kanban-btn"
			:scroll-into-view="'kanban'+(selectedIndex==0?0:(selectedIndex-1))">
			<view style="display: flex;flex-direction: row;align-items: center;">
				<view style="margin-left: 16rpx;margin-right: 16rpx;height: 80rpx;" v-for="item,index in kanbanList"
					:key="index" :id="'kanban'+index">
					<button :class="{'not-selected':index!=selectedIndex,'selected':index==selectedIndex}"
						class="select-btn" @click="confirmKanban(index)">
						{{item.label}}
					</button>
				</view>
			</view>
		</scroll-view>
		<view @click="showSelect=true"
			style="top:0;background: #fff;position: fixed;z-index:10; right: 0;border-bottom:#e7e7e7 solid 1rpx ; height: 100rpx;width:102rpx;display: flex;justify-content: center;align-items: center;">
			<text class="tn-icon-menu" style="font-size: 50rpx;"></text>
		</view>
		<!-- <view
			style="font-size: 40rpx;padding-left: 30rpx;width: 750rpx;background:#fff; position: fixed;padding-top:20rpx;z-index:999;top:80rpx"
			@click="showSelect=true">
			{{seletedKanban.label}}<text class="tn-icon-down" style="margin-left: 14rpx;"></text>
		</view> -->
		<view style="z-index: 1;" :style="{paddingTop: vuex_custom_bar_height + 10 +  'px',marginTop:'80rpx'}">
			<template>
				<view style="background:#fff;" v-for="(item,index) in kanbanDetail" :key="item.id">
					<view class="tn-flex tn-flex-row-between tn-flex-col-center">
						<view @click="popPick(item)" class="justify-content-item tn-margin tn-text-bold tn-text-lg">
							{{item.title}}<text class="tn-icon-down" style="font-size: 30rpx;margin-left: 10rpx;"></text>
						</view>
					</view>
					<scroll-view :scroll-x="true" :scroll-with-animation="true" class="" style="min-height: 500rpx;">
						<!-- <qiun-data-charts :ontouch="item.option.enableScroll" canvas2d :type="item.type" :opts="item.option" :chartData="item.chartData" /> -->
						<l-echart :ref="'chart'+item.id" @finished="init(item)" :style="(item.showType == '1' || !item.showType)?('width:' + (item.showHeight || 600) * (item.width / item.height) + 'rpx;height:' + (item
						.showHeight || 600) + 'rpx'):('width:750rpx;height:' + 750 * (item.height / item.width) + 'rpx')" :customStyle="(item.showType == '1' || !item.showType)?('width:' + (item.showHeight || 600) * (item.width / item.height) + 'rpx;height:' + (item
						.showHeight || 600) + 'rpx'):('width:750rpx;height:' + 750 * (item.height / item.width) + 'rpx')"></l-echart>
					</scroll-view>
					<view class="tn-strip-bottom"></view>
				</view>
			</template>
		</view>
		<tn-popup mode="top" style="z-index: 200;background-color: #fff;" v-model="showSelect">
			<view class="content">
				<scroll-view scroll-y="true" style="max-height: 900rpx;">
					<view style="display: flex;flex-wrap: wrap;padding-bottom: 20rpx;">
						<view style="margin-left: 16rpx;margin-top: 16rpx;" v-for="item,index in kanbanList"
							:key="index">
							<tn-button :fontSize="25" :plain="selectedIndex!=index" backgroundColor="tn-bg-blue"
								:fontColor="selectedIndex==index?'#fff': '#82B2FF'" @click="confirmKanban(index)">
								{{item.label}}
							</tn-button>
						</view>
					</view>
				</scroll-view>
			</view>
		</tn-popup>
		 <tn-empty :customStyle="{'margin-top': '25vh'}" v-if="kanbanDetail.length==0" mode="data"></tn-empty>
		<tn-picker mode="selector" v-model="showChoose" :defaultSelector="[menuSelectedIndex]" @confirm="comfirmPick"
			:range="['保存图片','以屏幕宽度自适应','以显示高度自适应']"></tn-picker>
	</view>
</template>

<script>
	import template_page_mixin from '@/libs/mixin/template_page_mixin.js'
	import LEchart from '@/pagesA/components/l-echart/l-echart.vue'
	export default {
		components: {
			LEchart,
		},
		mixins: [template_page_mixin],
		created() {
			//#ifdef MP-WEIXIN
			uni.setNavigationBarTitle({
				title: '看板'
			})
			//#endif
		},
		data() {
			return {
				navTop: 0,
				selectedIndex: 0,
				showSelect: false,
				showChoose: false,
				menuSelectedIndex:0,
				chooseItem: {},
				seletedKanban: {
					label: '请选择'
				},
				kanbanList: [],
				kanbanDetail: []
			}
		},
		onShow() {},
		onLoad() {},
		onReady() {
			// #ifdef H5||APP-PLUS
			this.navTop = 44
			// #endif
			this.getKanbanData()
		},
		mounted() {},
		methods: {
			popPick(item) {
				this.showChoose = true
				this.chooseItem = item
			},
			comfirmPick(select) {
				let e = this.chooseItem
				this.menuSelectedIndex = select[0]
				if (select[0] == 0) {
					this.$refs['chart' + e.rid][0].canvasToTempFilePath({
						success: function(res) {
							// #ifdef H5
							var oA = document.createElement("a");
							oA.download = '图片名称.png'; // 设置下载的文件名，默认是'下载'
							oA.href = res.tempFilePath;    //图片url
							document.body.appendChild(oA);
							oA.click();
							oA.remove(); 
							//#endif
							// #ifndef H5
							uni.saveImageToPhotosAlbum({
								filePath: res.tempFilePath, //图片url
								success: () => {
									uni.showToast({
										title: "保存成功",
										icon: "success"
									})
								}
							})
							//#endif
						}
					})
				}
				if (select[0] == 1) {
					this.$set(e, 'showType', '2')
					this.$nextTick(()=>{
						this.$refs['chart' + e.id][0].resize()
					})
				}
				if (select[0] == 2) {
					this.$set(e, 'showType', '1')
					this.$nextTick(()=>{
						this.$refs['chart' + e.id][0].resize()
					})
				}
			},
			confirmKanban(idx) {
				this.showSelect = false
				this.selectedIndex = idx
				this.seletedKanban = this.kanbanList[idx]
				this.getKanbanData(idx)
			},
			getKanbanData(idx) {
				let that = this
				let rid = ""
				if (this.kanbanList[idx]) {
					rid = this.kanbanList[idx].rid
				}
				this.xAjax({
					url: "wxDashboard.php?rid=" + rid
				}, (res)=> {
					if(res.kanbanDetail){
						this.kanbanDetail = res.kanbanDetail
					}
					if(res.kanbanList){
						this.kanbanList = res.kanbanList
					}
				})
			},
			async init(e) {
				this.$refs['chart' + e.id][0].setOption(JSON.parse(e.option));
			},
		},
		onPullDownRefresh() {
			this.selectedIndex = 0
			this.getKanbanData()
			
		}
	};
</script>
<style lang="scss" scoped>
	.kanban-btn {
		top: 0;
		width: calc(100% - 100rpx);
		height: 100rpx;
		display: flex;
		flex-direction: row;
		overflow-x: scroll;
		position: fixed;
		background-color: #fff;
		z-index: 5;
		border-bottom: #e7e7e7 solid 1rpx;
	}

	.select-btn {
		font-size: 34rpx;
		display: flex;
		flex-direction: row;
		white-space: nowrap;
		align-items: center;
		padding-left: 0;
		padding-right: 0;
	}

	.selected {
		background: #fff;
		color: #82B2FF;
	}

	.selected::after {
		content: '';
		width: 80%;
		border-radius: 0;
		left: 25%;
		border-bottom: solid 6rpx #82B2FF;
	}

	.not-selected {
		background: #fff;
		color: #000;
	}

	/* 间隔线 start*/
	.tn-strip-bottom {
		width: 100%;
		border-bottom: 20rpx solid #F8F7F8;
	}

	/* 间隔线小 start*/
	.tn-strip-bottom-min {
		width: 100%;
		border-bottom: 1rpx solid #F8F7F8;
	}
</style>