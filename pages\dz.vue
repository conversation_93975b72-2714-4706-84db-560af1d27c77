<template>
	<view style="padding-bottom: 110rpx;">
		<view class="title">单击选中/拖动排序</view>
		<view style="margin-top:90rpx;">
			<movable-area class="drag-sort" :style="{height:boxHeight }" id="drag">
			<movable-view
			v-for="(item, index) in currentList"
			:key="index"
			:x="item.x"
			v-if="item.isShow === 1"
			:data-index="index"
			@touchstart="touchstart"
			@touchmove.stop="touchmove"
			@touchend="touchend"
			@click="check(index)"
			:y="item.y"
			:direction="direction"
			disabled
			damping="40"
			:animation="item.animation"
			class="drag-sort-item"
			:class="{'selected':item.selected}">
				<view class="item">
					<text>{{item.label}}</text>
				</view>
			</movable-view>
			</movable-area>
		</view>
		<view class="btnContainer">
			<button class="searchBtn" type="default" @click="dzReset" style="background-color: #909399;color: #FFFFFF;">恢复成默认</button>
			<button class="searchBtn" type="default" @click="dzSubmit" style="background-color: #409EFF;color: #FFFFFF;">仅本次生效</button>
			<button class="searchBtn" type="default" @click="dzSubmitSave" style="background-color: #67C23A;color: #FFFFFF;">生效并保存</button>
		</view>
	</view>

</template>

<script>
	export default {
		data () {
			return {
				id:'',
				list:[],
				cols:'',
				direction:"all",
				windowWidth:100, //屏幕宽度
				height: 40, // 高度
				currentList: [],
				active: -1, // 当前激活的item
				index: 0, // 当前激活的item的原index
				topY: 0, // 距离顶部的距离
				topX: 0, // 距离左侧偏移位置
				deviationX: 0,
				deviationY: 0// 偏移量
			}
		},
		computed: {
			boxHeight(){
				return (Math.ceil((Number(this.list.length)+1)/4)) * this.height + 'px'	
			}
		},
		watch: {
			list:{
				handler(){
					//debugger
					this.onUpdateCurrentList()
				},
				deep:true
				
			}
		},
		created () {
			this.windowWidth = uni.getWindowInfo().windowWidth
			this.onUpdateCurrentList()
		},
		mounted () {
		},
		updated () {},
		filters: {},
		methods: {
			onUpdateCurrentList (list = this.list) {
				let arr = []
				for (const key in list) {
					// console.log(key)
					let height =	Math.ceil((Number(key)+1)/4) - 1
					let x = 0
					if(key <= 3){
						x = key * this.windowWidth * 0.24 + this.windowWidth * 0.04 || this.windowWidth * 0.04
					}else{
						if((Number(key)+1)%4 === 0){
							 x = 3 * this.windowWidth * 0.24 + this.windowWidth * 0.04 || this.windowWidth * 0.04
						}else{
							x = ((Number(key)+1)%4-1) * this.windowWidth * 0.24 + this.windowWidth * 0.04 || this.windowWidth * 0.04
						}
						
					}
					arr.push({
						...list[key],
						isShow:1,
						index: Number(key),
						SortNumber:Number(key),
						y: height * this.height,
						x,
						animation: true
					})
				}
				this.currentList = arr
			},
			// 根据排序进行重新计算位置
			moveUpdateCurrentList(index){
				for (const i in this.currentList) {
					let key
					if(this.currentList[i].SortNumber || this.currentList[i].SortNumber === 0){
						key = this.currentList[i].SortNumber
					}else{
						key = Number(i)
					}
					// console.log(key)
					let temobj = { ...this.currentList[i] }
					 // debugger
					this.currentList[i].y =	(Math.ceil((Number(key)+1)/4) - 1)*this.height 
					if(index == key){
						continue
					}else{
						if(key <= 3){
							this.currentList[i].x = key * this.windowWidth * 0.24 + this.windowWidth * 0.04 || this.windowWidth * 0.04
						}else{
							if((Number(key)+1)%4 === 0){
								 this.currentList[i].x = 3 * this.windowWidth * 0.24 + this.windowWidth * 0.04 || this.windowWidth * 0.04
							}else{
								 this.currentList[i].x = ((Number(key)+1)%4-1) * this.windowWidth * 0.24 + this.windowWidth * 0.04 || this.windowWidth * 0.04
							}
						} 
					}
				}
				this.$emit('change', this.currentList)
			},
			touchstart (e) {
				// 计算 x y 轴点击位置
				var query = uni.createSelectorQuery().in(this)
				query.select('#drag').boundingClientRect()
				query.exec((res) => {
					this.topY = res[0].top
					this.topX	= res[0].left
					let touchY = e.mp.touches[0].clientY - res[0].top
					let touchX = e.mp.touches[0].clientX - res[0].left
					this.deviationY = touchY % this.height
					 this.deviationX = touchX % (this.windowWidth*0.2)
					this.active = Number(e.currentTarget.dataset.index)
					this.index = Number(e.currentTarget.dataset.index)
				})
			},
			touchmove (e) {
				if (this.active < 0) return
				let temY = e.mp.touches[0].clientY - this.topY
				let temX = e.mp.touches[0].clientX - this.topX
				let touchY = temY - 15
				let touchX = temX - this.windowWidth*0.1
				this.currentList[this.active].y = touchY 
				this.currentList[this.active].x = touchX
				this.currentList[this.active].animation = false
				this.currentList.every( (res, index) => {
					let absX =	Math.abs(touchX - res.x)
					let absY =	Math.abs(touchY - res.y)
					// 设置元素定点距离多少进行重排
					if( 0 < absX && absX <= 10 && absY > 0 && absY <= 10 &&　this.active　!=　index){
						// debugger
						let temNumber = this.currentList[index].SortNumber
						this.currentList.every( (_res, _index) => {
							// 判断从大像小移还是从小向大移
							if( this.currentList[this.active].SortNumber < this.currentList[index].SortNumber){
								// 移动元素比目标元素所在位置小，之间元素排序--
								if( this.currentList[_index].SortNumber > this.currentList[this.active].SortNumber && this.currentList[_index].SortNumber <= this.currentList[index].SortNumber){
									_res.SortNumber--
								}
							}else{
								// 反之++
								if( this.currentList[_index].SortNumber < this.currentList[this.active].SortNumber && this.currentList[_index].SortNumber >= this.currentList[index].SortNumber){
									_res.SortNumber++
								}
							}
							return true
						}, this)
						this.currentList[this.active].SortNumber = temNumber 
						this.moveUpdateCurrentList(temNumber)
						return false
					}else{
						return true
					}
				}, this)
			},
			touchend (e) {
				if(this.currentList[this.active]){
					this.currentList[this.active].animation = true
				}
				this.moveUpdateCurrentList(-1)
				this.active = -1
			},
			// 选中
			check(index){
				this.$set(this.currentList[index],'selected',!this.currentList[index].selected)
				let cols=[]
				let len=this.currentList.length
				for(let s=0;s< len; s++){
					for(let i=0;i< len;i++){
						if(this.currentList[i].selected&&this.currentList[i].SortNumber==s){
							cols.push(this.currentList[i].name)
						}
					}
				}
				this.cols=cols.join(',')
			},
			initDZColumn() {
				let that=this
				let url='wxGetDZColumn.php?id='+this.id+'&cols='+this.cols
				this.xAjax({url:url},function(res){
					that.list=res
				})
				this.onUpdateCurrentList()
			},
			dzReset() {
				let len=this.currentList.length
				for(let i=0;i< len;i++){
					if(this.currentList[i].default){
						this.$set(this.currentList[i],'selected',true)
					}else{
						this.$set(this.currentList[i],'selected',false)
					}
				}
				this.cols=''
			},
			dzSubmit() {
				if(this.cols){
					uni.$emit(this.id+'dzSubmit',this.cols)
				}
				uni.navigateBack()
			},
			dzSubmitSave() {
				this.dzSubmit()
				let url='wxListSaveConf.php?id='+this.id+'&src=2&do=save&cols='+this.cols
				this.xAjax({url:url})
			}
		},
		onLoad(e) {
			this.id=e.id
			this.cols=e.cols
			this.initDZColumn()
		}
	}
</script>

<style>

	.drag-sort {
		width: 100%;
	}
	
	.drag-sort-item {
		position: absolute !important;
		display: flex;
		height: 30px;
		align-items: center;
		width: 20%;
		text-align: center;
		background-color: #EFEFF4;
		border:1px solid #EFEFF4;
		color: #000;
		border-radius: 5px;
		box-sizing: border-box;
	}

	.item {
		position: relative;
		flex: 1;
		font-size: 10px;
	}

	.selected {
		background-color: #c6e2ff;
		border:1px solid #409EFF;
		color: #409EFF;
	}

	.title{
		position:fixed;
		top:0;
		text-align: center;
		color: grey;
		padding: 20rpx;
		background: #FFFFFF;
		margin-bottom: 15px;
		position:fixed;
		top:0;
		z-index: 999;
		height: 18px;
		width:100%;
		font-size: 26rpx;
	}

	.btnContainer {
		display: flex;
		align-items: center;
		width:100%;
		z-index:10001;
		flex: 1;
		flex-direction: row;
		position: fixed;
		bottom:0rpx;
		background-color: #FFFFFF;
		border-bottom-left-radius: 25rpx;
		height: 90rpx;
	}

	.searchBtn {
		padding: 0 5px;
		height: 70rpx;
		font-size: 28rpx;
		border-radius: 35rpx;
		width:200rpx;
	}
	
</style>
