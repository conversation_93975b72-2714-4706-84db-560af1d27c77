<template>
	<view>
		<fttx-list ref="list"></fttx-list>
	</view>
</template>

<script>
	import fttxList from '@/pages/list.vue';
	export default {
		components: {
			fttxList
		},
		data:function() {
			return {
				id:'order_room',
				label:'巡检任务'
			}
		},
		methods: {
			
		},
		onLoad(e){
			this.$nextTick(()=>{
    			this.$refs.list.id=this.id;
    			this.$refs.list.label=this.label;
    			this.$refs.list.initList();
    		})
		},
		onPullDownRefresh() {
			this.$nextTick(()=>{
			    this.$refs.list.pullDownToRefresh();
    		})
		},
		onReachBottom() {
			this.$nextTick(()=>{
			    this.$refs.list.pullUpLoadMore();
    		})
		}
	}
</script>
