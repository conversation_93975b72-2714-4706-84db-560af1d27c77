<template>
	<view class="warp">
		<view style="width:100%;height:260rpx;background-color: #fff;">
			<view style="position: absolute;top:45rpx;left:60rpx;">
				<image src="/static/img/user-avatar.png" class="headerImage"></image>
			</view>
			<view style="position: absolute;top:80rpx;left:240rpx;font-weight: 600;font-size:25px">{{userName}}</view>
			<view style="position: absolute;top:190rpx;left:60rpx;font-weight: 500;color:#777">{{dept}}</view>
		</view>
		<view class="functions">
			<view style="width:100%">
				<view class="padding-top-bottom">
					<tn-list-cell v-for="(item,idx) in setList" :hover="true" :unlined="true" :radius="true"
						:fontSize="30" @click="changeSet(item)">
						<view class="tn-flex tn-flex-col-center">
							<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-color-white"
								:class="'tn-cool-bg-' + item.bgcolor"> <!-- 图鸟定义的酷色tn-cool-bg-color-1 -->
								<view :class="'tn-icon-'+ item.icon"></view>
							</view>
							<view class="tn-margin-left-sm tn-flex-1">{{item.text}}</view>
							<uni-badge class="uni-badge-left-margin" :text="item.badge" absolute="rightTop"
								style="margin-top:-12px" :offset="[1, 1]" size="small"></uni-badge>
							<view class="tn-margin-left-sm tn-color-gray tn-icon-right"></view>
						</view>
					</tn-list-cell>
				</view>
			</view>
		</view>
		<view class="functions" style="margin-top:0rpx">
			<view style="width:100%">
				<view class="padding-top-bottom">
					<tn-list-cell v-if="isAdmin" :hover="true" :unlined="true" :radius="true" :fontSize="30"
						@click="changeAccount()">
						<view class="tn-flex tn-flex-col-center">
							<view
								class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-cool-bg-color-6 tn-color-white">
								<view class="tn-icon-team-fill"></view>
							</view>
							<view class="tn-margin-left-sm tn-flex-1">切换账号</view>
							<view class="tn-margin-left-sm tn-color-gray tn-icon-right"></view>
						</view>
					</tn-list-cell>
					<!-- #ifdef APP -->
					<tn-list-cell :hover="true" :unlined="true" :radius="true" :fontSize="30" @click="soterAuth()">
						<view class="tn-flex tn-flex-col-center">
							<view
								class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-cool-bg-color-1 tn-color-white">
								<view class="tn-icon-fingerprint"></view>
							</view>
							<view class="tn-margin-left-sm tn-flex-1">指纹登录</view>
							<view class="tn-margin-left-sm tn-color-gray tn-icon-right"></view>
						</view>
					</tn-list-cell>
					<!-- #endif -->
					<tn-list-cell :hover="true" :unlined="true" :radius="true" :fontSize="30" @click="logOut()">
						<view class="tn-flex tn-flex-col-center">
							<view
								class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-cool-bg-color-1 tn-color-white">
								<view class="tn-icon-power"></view>
							</view>
							<view class="tn-margin-left-sm tn-flex-1">退出登录</view>
							<view class="tn-margin-left-sm tn-color-gray tn-icon-right"></view>
						</view>
					</tn-list-cell>
				</view>
			</view>
		</view>
	</view>
</template>


<script>
	import {soterAuth} from '../static/js/soterAuth.js'
	import {
		mapState,
		mapMutations
	} from 'vuex'
	export default {
		components: {},
		data() {
			return {
				title: '我的',
				userName: '',
				dept: '',
				isAdmin: 0,
				setList: [{
						icon: 'help',
						bgcolor: 'color-6',
						url: 'link',
						text: '操作指南',
					},
					{
						id: 'bulletin',
						icon: 'sound',
						bgcolor: 'color-2',
						url: 'list',
						text: '历史公告'
					},
					{
						icon: 'phone-fill',
						bgcolor: 'color-9',
						url: 'bindMobile',
						text: '绑定手机'
					},
					{
						icon: 'star-fill',
						bgcolor: 'color-13',
						url: 'cacheList',
						text: '离线列表'
					},
					{
						icon: 'image-fill',
						bgcolor: 'color-12',
						url: 'cachePic',
						text: '离线图片'
					},
					{
						icon: 'monitor-fill',
						bgcolor: 'color-14',
						url: 'inductionDevice',
						text: '感应装置'
					},
					{
						icon: 'camera-fill',
						bgcolor: 'color-15',
						url: 'shot?login=1',
						text: '随手拍'
					}
				]
			}
		},
		computed: {},
		methods: {
			changeSet(e) {
				let url = e.url
				if (url == 'link') {
					url = 'link?label=操作手册&url=wxGuide.php'
				} else if (e.id) {
					url = 'list?id=' + e.id + '&label=' + e.text
				}
				uni.navigateTo({
					url: url
				})
			},
			changeAccount() {
				uni.navigateTo({
					url: 'changeUser'
				})
			},
			soterAuth(){
				soterAuth('mine',()=>{
					uni.setStorage({
						key: 'isSoterAuth',
						data: 1
					})
				});
			},
			logOut() {
				if (getApp().globalData.offline) { //离线模式直接退出
					getApp().globalData.offline = false
					uni.reLaunch({
						url: 'login'
					})
					return
				}
				var that = this
				uni.showModal({
					content: '是否退出登录',
					success(res) {
						if (res.confirm) {
							var url = "logout.php"
							that.xAjax({
								url: url,
								hideAll: true
							}, function(res) {
								if (res.ok) {
									uni.reLaunch({
										url: 'login'
									})
								}
							}, function() {
								uni.reLaunch({
									url: 'login'
								})
							})
						}
					}
				})

			}
		},
		onLoad() {
			uni.$on('setMineOnline', function(e) {
				uni.setNavigationBarTitle({
					title: '我的'
				})
			})
			this.isAdmin = (getApp().globalData.isAdmin || uni.getStorageSync('isAdmin')) == 1
			//this.$set(this.setList[7], 'badge', getApp().globalData.sysMessageCount)
			this.userName = getApp().globalData.userName || uni.getStorageSync('userName')
			this.dept = getApp().globalData.dept || uni.getStorageSync('dept')
		},
		onShow() {
			if (getApp().globalData.offline) {
				this.title = '我的（离线）'
			} else {
				this.title = '我的'
			}
			uni.setNavigationBarTitle({
				title: this.title
			})
			let that = this
			// uni.$on('setMessageCount',(e)=>{
			// 	that.$set(that.setList[7], 'badge', e)
			// })
		},
		onPullDownRefresh() {}
	}
</script>

<style lang="scss" scoped>
	.warp {
		display: flex;
		flex-direction: column;
		justify-content: start;
		align-items: center;
		min-height: 100vh;
		background: linear-gradient(to bottom, #bedeff, #ffffff); //e6f4fa
	}

	.functions {
		width: 700rpx;
		display: flex;
		margin: 25rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		font-size: 32rpx;
		padding: 0;
	}

	.headerImage {
		height: 130rpx;
		width: 130rpx;
		border-radius: 75rpx;
	}

	/* 页面 start*/
	.about-shadow {
		border-radius: 15rpx;
		box-shadow: 0rpx 0rpx 50rpx 0rpx rgba(0, 0, 0, 0.07);
	}

	.about {

		&__wrap {
			position: relative;
			z-index: 1;
			margin: 20rpx 0rpx;
			margin-top: -180rpx;
		}
	}

	/* 页面 end*/

	/* 用户信息 start */
	.user-info {
		&__container {}

		&__avatar {
			width: 180rpx;
			height: 180rpx;
			border: 8rpx solid rgba(255, 255, 255, 0.05);
			border-radius: 50%;
			overflow: hidden;
			box-shadow: 0rpx 0rpx 80rpx 0rpx rgba(0, 0, 0, 0.15);
		}

		&__nick-name {
			margin-top: 26rpx;
			font-size: 42rpx;
			font-weight: 600;
			text-align: center;
		}
	}

	/* 用户信息 end */

	.padding-top-bottom {
		padding-bottom: 20rpx;
		padding-top: 20rpx;
	}

	/* 信息展示 start */
	.tn-info {

		&__container {
			margin-top: 0rpx;
		}

		&__item {
			width: 48%;
			margin: 15rpx 0rpx;
			padding: 28rpx;
			border-radius: 15rpx;
			position: relative;
			z-index: 1;

			&::after {
				content: " ";
				position: absolute;
				z-index: -1;
				width: 100%;
				height: 100%;
				left: 0;
				bottom: 0;
				border-radius: inherit;
				opacity: 1;
				transform: scale(1, 1);
				background-size: 100% 100%;
				//background-image: url(https://tnuiimage.tnkjapp.com/cool_bg_image/6.png);
			}

			&__left {

				&--icon {
					width: 80rpx;
					height: 80rpx;
					border-radius: 50%;
					font-size: 40rpx;
					margin-right: 20rpx;
					position: relative;
					z-index: 1;

					&::after {
						content: " ";
						position: absolute;
						z-index: -1;
						width: 100%;
						height: 100%;
						left: 0;
						bottom: 0;
						border-radius: inherit;
						opacity: 1;
						transform: scale(1, 1);
						background-size: 100% 100%;
						//background-image: url(https://tnuiimage.tnkjapp.com/cool_bg_image/icon_bg5.png);
					}
				}

				&__content {
					font-size: 30rpx;

					&--data {
						margin-top: 5rpx;
						font-weight: bold;
					}
				}
			}

			&__right {
				&--icon {
					font-size: 60rpx;
					opacity: 0.15;
				}
			}
		}
	}

	/* 信息展示 end */

	/* 图标容器1 start */
	.icon1 {
		&__item {
			// width: 30%;
			background-color: #FFFFFF;
			border-radius: 10rpx;
			padding: 30rpx;
			margin: 20rpx 10rpx;
			transform: scale(1);
			transition: transform 0.3s linear;
			transform-origin: center center;

			&--icon {
				width: 40rpx;
				height: 40rpx;
				font-size: 28rpx;
				border-radius: 50%;
				position: relative;
				z-index: 1;

				&::after {
					content: " ";
					position: absolute;
					z-index: -1;
					width: 100%;
					height: 100%;
					left: 0;
					bottom: 0;
					border-radius: inherit;
					opacity: 1;
					transform: scale(1, 1);
					background-size: 100% 100%;
					//background-image: url(https://tnuiimage.tnkjapp.com/cool_bg_image/icon_bg.png);
				}
			}
		}
	}

	/* 图标容器1 end */
</style>