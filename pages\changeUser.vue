<template>
	<view class="content">
		<uni-search-bar style="width:94%;position: sticky;top:10rpx;z-index: 10;"
			cancelButton=none
			radius="50"
			focus
			placeholder="输入名字或账号搜索"
			bg-color="#fff" v-model="keyword">
		</uni-search-bar>
		<view v-for="user in userData" class="user-item" :key="user.id">
			<view style="font-weight: 600;font-size: 34rpx;margin-left: 20rpx;">
				{{user.value}}
			</view>
			<tn-button style="margin-right: 20rpx;" width="120rpx" height="60rpx" @click="changeUser(user.id)" backgroundColor="tn-bg-blue" fontColor="tn-color-white">切换</tn-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				keyword:'',
				userData:[],
			}
		},
		watch:{
			keyword(val){
				clearTimeout(this.searchTimer)
				this.searchTimer = setTimeout(()=>{
					if(val){
						this.searchUser()
					}else{
						this.userData = []
					}
				},300)
			}
		},
		methods: {
			searchUser(){
				this.xAjax({
					url: 'getSwitchUser.php',
					data:{keyword:this.keyword}
				}, (res) =>{
					this.userData = res
				})
			},
			changeUser(id){
				this.xAjax({
					url: 'setSwitchUser.php',
					data:{id:id}
				}, (res) =>{
					getApp().globalData.userName = res.userName
					uni.setStorageSync('userName', res.userName)
					getApp().globalData.userId = res.userid
					uni.setStorageSync('userId', res.userid)
					getApp().globalData.dept = res.dept
					uni.setStorageSync('dept', res.dept)
					getApp().globalData.isAdmin = 0
					uni.setStorageSync('isAdmin', 0)
					uni.reLaunch({
					  url: 'main' 
					});
				})
				
			}
		}
	}
</script>

<style scoped>
.content{
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	background-color: #f4f5ff;
	min-height: 100vh;
}
.user-item{
	width: 94%;
	display: flex;
	height: 100rpx;
	border-radius: 20rpx;
	margin-top: 16rpx;
	align-items: center;
	justify-content: space-between;
	background-color: #fff;
}
</style>
