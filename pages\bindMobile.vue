<template>
	<view class="template-login">
		<view class="login">
		
			<!-- 输入框内容-->
			<view class="login__info tn-flex tn-flex-direction-column tn-flex-col-center tn-flex-row-center">
				<!-- 登录 -->
				<block>
					<view class="login__info__item__input tn-flex tn-flex-direction-row tn-flex-nowrap tn-flex-col-center tn-flex-row-left">
						<view class="login__info__item__input__left-icon">
							<view class="tn-icon-phone"></view>
						</view>
						<view class="login__info__item__input__content">
							<input v-model="phoneNumber" :disabled="disablePhone" :focus="phoneNumberFocus" @blur="phoneNumberBlur" maxlength="20" placeholder="请输入手机号" />
						</view>
						<view v-if="disablePhone" style="position: absolute;top:0;color:red">该账号已绑定手机，先解绑后再重新绑定</view>
					</view>
					<view class="login__info__item__input tn-flex tn-flex-direction-row tn-flex-nowrap tn-flex-col-center tn-flex-row-left">
						<view class="login__info__item__input__left-icon">
							<view class="tn-icon-safe"></view>
						</view>
						<view class="login__info__item__input__content login__info__item__input__content--verify-code">
							<input v-model="smsCode" :focus="smscodeFocus" @blur="smscodeBlur" maxlength="6" placeholder="请输入短信码" />
						</view>
						<view class="login__info__item__input__right-verify-code" @tap.stop="getCode">
							<tn-button backgroundColor="tn-bg-blue" fontColor="#FFFFFF" size="sm" padding="5rpx 10rpx" width="100%" shape="round">{{ tips }}</tn-button>
						</view>
					</view>
				</block>
		
				<view @click="bindMobile" class="login__info__item__button tn-color-white" :class="disablePhone?'tn-bg-orangered':'tn-bg-blue'">{{disablePhone?'解绑':'绑定'}}</view>
	      
			</view>
		</view>
		<!-- 验证码倒计时 -->
		<tn-verification-code
			ref="code"
			:seconds="60"
			@change="codeChange">
		</tn-verification-code>
	</view>
</template>

<script>
	import JSEncrypt from '../static/js/jsencrypt.min.js'
	var en = new JSEncrypt()
	en.setPublicKey(
		"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC8HMr2CBpoZPm3t9tCVlrKtTmI4jNJc7/HhxjIEiDjC8czP4PV+44LjXvLYcSV0fwi6nE4LH2c5PBPEnPfqp0g8TZeX+bYGvd70cXee9d8wHgBqi4k0J0X33c0ZnW7JruftPyvJo9OelYSofBXQTcwI+3uIl/YvrgQRv6A5mW01QIDAQAB"
	)
    export default {
        data() {
            return {
                phoneNumber: "",
				disablePhone:false,
				smsCode: "",
				phoneNumberFocus: false,
				smscodeFocus: false,
				tips: '获取短信码',
				smsSendStatus:false,
				weixinCode: ''
            }
        },
		onLoad(e){
			let that=this
			let url="wxGetBindMobile.php"
			that.xAjax({
				url:url
			},function(res){
				if(res.mobile!=""){
					that.phoneNumber=res.mobile
					that.disablePhone=true
				}
			})
		},
		methods: {
			phoneNumberBlur(){
				this.phoneNumberFocus=false
			},
			smscodeBlur(){
				this.smscodeFocus=false
			},
			// 获取验证码
			getCode() {
				let that=this
				if(!this.phoneNumber){
					this.$tn.message.toast("请输入手机号")
					this.phoneNumberFocus=true
					return
				}
				if(!this.disablePhone&&!(/^1[3456789]\d{9}$/.test(this.phoneNumber))){
					this.$tn.message.toast("请填写正确的手机号码")
					this.phoneNumberFocus=true
					return
				}
				this.smsCode=''
				this.smscodeFocus=true
				if (this.$refs.code.canGetCode) {//图鸟组件自带的参数
					// #ifdef MP-WEIXIN
					uni.login({
						provider: 'weixin',
						success: (res) => {
							this.weixinCode = res.code
							this.sendSms()
						},
						fail: (err) => {
							alert(err)
						}
					})
					// #endif
					// #ifndef MP-WEIXIN
					this.sendSms();
					// #endif
				} else {
					this.$tn.message.toast(this.$refs.code.secNum + '秒后再重试')
				}
			},
			sendSms(){
				let that=this
				let data={
					phoneNumber: en.encrypt(this.phoneNumber)
				}
				this.xAjax({
					url:"wxGetSmsCode.php",
					data:data
				},function(res){
					if(res.ok){
						that.$refs.code.start()
						that.smsSendStatus=true
					}else{
						that.$tn.message.toast("短信发送失败")
					}
				})
			},
			// 获取验证码倒计时被修改
			codeChange(event) {
				this.tips = event
			},
            bindMobile() {
				let that=this
				if(!this.smsSendStatus){
					this.$tn.message.toast("请先获取短信码")
					return
				}
				if(this.smsCode==""){
					this.showErr('请输入短信码')
					return
				}
				let data = 'smsCode='+this.smsCode
				let url='wxBindMobile.php'
				if(this.disablePhone){
					url='wxFreeMobile.php'
				}
				this.xAjax({
					url:url, 
					data:data,
				},function(res){
					if(res.ok){
						if(that.disablePhone){
							that.phoneNumber=''
							that.disablePhone=false
							return
						}
						let pages = getCurrentPages();
						console.log(pages)
						if(pages[0].route=='pages/mine'){
							uni.navigateBack()
						}else{
							uni.reLaunch({
								url:'main'
							})
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	
	.login {
		position: relative;
		height: 100%;
		z-index: 1;
	
		&__info {
			margin: 30rpx 30rpx 10rpx 30rpx;
			padding-bottom: 0;
			border-radius: 20rpx;
	  
			&__item {
	    
				&__input {
					margin-top: 59rpx;
					width: 90%;
					height: 77rpx;
					border: 1rpx solid #E6E6E6;
					border-radius: 39rpx;
	      
					&__left-icon {
						width: 10%;
						font-size: 44rpx;
						margin-left: 20rpx;
						color: #838383;
					}
	  
					&__content {
						width: 80%;
						padding-left: 10rpx;
		
						&--verify-code {
							width: 56%;
						}
	        
						input {
							font-size: 24rpx;
							// letter-spacing: 0.1em;
						}
					}
	
					&__right-verify-code {
						width: 34%;
						margin-right: 20rpx;
					}
				}
	    
				&__button {
					margin-top: 75rpx;
					margin-bottom: 39rpx;
					width: 90%;
					height: 77rpx;
					text-align: center;
					font-size: 31rpx;
					font-weight: bold;
					line-height: 77rpx;
					letter-spacing: 1em;
					text-indent: 1em;
					border-radius: 39rpx;
					box-shadow: 1rpx 10rpx 24rpx 0rpx rgba(60, 129, 254, 0.35);
				}
	    
				&__tips {
					margin: 30rpx 0;
					color: #AAAAAA;
				}
			}
		}
	}
	
</style>